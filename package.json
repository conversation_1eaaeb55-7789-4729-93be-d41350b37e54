{"name": "8d8ae1dc89db0932987d68a77584cfc046541661bc7cfbad167bf6f6fe81207f", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@kombai/react-error-boundary": "^1.1.0", "@popperjs/core": "^2.11.8", "bootstrap": "^5.3.8", "date-fns": "^4.1.0", "firebase": "^12.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.9.1", "react-router-dom": "^6.26.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^24.5.2", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.7.0", "comment-json": "^4.2.5", "env-cmd": "^11.0.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "rollup": "^4.44.1", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-plugin-static-copy": "^3.1.2", "vite-tsconfig-paths": "^5.1.4"}, "strictDependencies": {"@tailwindcss/vite": "^4.1.13", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.9.1", "tailwindcss": "^4.1.13", "@eslint/js": "^9.35.0", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^5.0.2", "eslint": "^9.35.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.43.0"}}