rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - users can read/write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Chat rooms collection - simplified for testing
    match /chatRooms/{chatId} {
      allow read, write: if request.auth != null;
    }

    // Messages collection - simplified for testing
    match /messages/{messageId} {
      allow read, write: if request.auth != null;
    }

    // Typing indicators collection - simplified for testing
    match /typing/{typingId} {
      allow read, write: if request.auth != null;
    }

    // Tasks collection (if exists)
    match /tasks/{taskId} {
      allow read, write: if request.auth != null;
    }

    // Rewards collection (if exists)
    match /rewards/{rewardId} {
      allow read, write: if request.auth != null;
    }

    // Families collection (if exists)
    match /families/{familyId} {
      allow read, write: if request.auth != null;
    }
  }
}
