rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - users can read/write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Chat rooms collection
    match /chatRooms/{chatId} {
      // Users can read chat rooms they participate in
      allow read: if request.auth != null && 
        request.auth.uid in resource.data.participants;
      
      // Users can create chat rooms
      allow create: if request.auth != null && 
        request.auth.uid in request.resource.data.participants &&
        request.auth.uid == request.resource.data.createdBy;
      
      // Users can update chat rooms they participate in
      allow update: if request.auth != null && 
        request.auth.uid in resource.data.participants;
      
      // Only chat creator can delete chat rooms
      allow delete: if request.auth != null && 
        request.auth.uid == resource.data.createdBy;
    }

    // Messages collection
    match /messages/{messageId} {
      // Users can read messages from chat rooms they participate in
      allow read: if request.auth != null && 
        request.auth.uid in get(/databases/$(database)/documents/chatRooms/$(resource.data.chatId)).data.participants;
      
      // Users can create messages in chat rooms they participate in
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.senderId &&
        request.auth.uid in get(/databases/$(database)/documents/chatRooms/$(request.resource.data.chatId)).data.participants;
      
      // Users can update their own messages
      allow update: if request.auth != null && 
        request.auth.uid == resource.data.senderId;
      
      // Users can delete their own messages
      allow delete: if request.auth != null && 
        request.auth.uid == resource.data.senderId;
    }

    // Typing indicators collection
    match /typing/{typingId} {
      // Users can read typing indicators from chat rooms they participate in
      allow read: if request.auth != null && 
        request.auth.uid in get(/databases/$(database)/documents/chatRooms/$(resource.data.chatId)).data.participants;
      
      // Users can create/update their own typing indicators
      allow create, update: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
      
      // Users can delete their own typing indicators
      allow delete: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }

    // Tasks collection (if exists)
    match /tasks/{taskId} {
      allow read, write: if request.auth != null;
    }

    // Rewards collection (if exists)
    match /rewards/{rewardId} {
      allow read, write: if request.auth != null;
    }

    // Families collection (if exists)
    match /families/{familyId} {
      allow read, write: if request.auth != null;
    }
  }
}
