# Firebase Setup for SuperTarefa

This document describes the Firebase configuration for the SuperTarefa application.

## Files Created

### 1. `firestore.rules`
- Defines security rules for Firestore collections
- Allows authenticated users to read/write their own data
- Restricts chat access to participants only
- Protects user privacy and data security

### 2. `firestore.indexes.json`
- Defines composite indexes required for complex queries
- Enables efficient querying of chat rooms by participants and activity status
- Supports message ordering by timestamp
- Required for proper chat functionality

### 3. `firebase.json`
- Firebase project configuration
- Defines hosting, Firestore, and emulator settings
- Configures build and deployment options

## Deployment Commands

### Deploy Firestore Rules and Indexes
```bash
firebase deploy --only firestore:rules,firestore:indexes
```

### Deploy Full Project
```bash
firebase deploy
```

### Start Local Emulators (Development)
```bash
firebase emulators:start
```

## Collections Structure

### `users`
- User profiles and authentication data
- Each user can only access their own profile

### `chatRooms`
- Chat room definitions and metadata
- Access restricted to participants only
- Requires composite index: `participants` (array-contains) + `isActive` + `updatedAt`

### `messages`
- Individual chat messages
- Access restricted to chat room participants
- Requires composite index: `chatId` + `timestamp`

### `typing`
- Real-time typing indicators
- Temporary documents for typing status
- Access restricted to chat participants

## Security Rules Summary

- **Authentication Required**: All operations require valid authentication
- **User Isolation**: Users can only access their own profile data
- **Chat Participation**: Chat access restricted to participants only
- **Message Ownership**: Users can only edit/delete their own messages
- **Privacy Protection**: No cross-user data access allowed

## Troubleshooting

### Index Errors
If you see "The query requires an index" errors:
1. Check that `firestore.indexes.json` is properly configured
2. Deploy indexes: `firebase deploy --only firestore:indexes`
3. Wait for index creation (can take several minutes)

### Permission Errors
If you see "Missing or insufficient permissions" errors:
1. Check that `firestore.rules` is properly configured
2. Deploy rules: `firebase deploy --only firestore:rules`
3. Verify user authentication is working

### Network Errors
If you see `ERR_BLOCKED_BY_CLIENT` errors:
1. Check browser ad blockers or security extensions
2. Verify Firebase configuration in `src/config/firebase.ts`
3. Check network connectivity to Firebase services
