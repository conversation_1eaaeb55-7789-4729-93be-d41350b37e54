import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useChat } from '../contexts/ChatContext';
import MobileNavbar from '../components/MobileNavbar';
import { DashboardSection } from '../types/enums';

/**
 * ChatPage component - Clean adult chat interface
 */
const ChatPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser, userProfile } = useAuth();
  const { chatRooms, messages, sendMessage, createChatRoom } = useChat();

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [showMobileChat, setShowMobileChat] = useState(false);
  const [activeSection, setActiveSection] = useState<DashboardSection>(DashboardSection.CHAT);

  // Get current messages
  const currentMessages = selectedChatId ? messages[selectedChatId] || [] : [];
  const currentChatRoom = selectedChatId ? chatRooms.find(room => room.id === selectedChatId) : null;

  // Auto-select first chat or create one
  useEffect(() => {
    if (chatRooms.length > 0 && !selectedChatId) {
      setSelectedChatId(chatRooms[0].id);
    } else if (chatRooms.length === 0 && currentUser) {
      createChatRoom('Chat Familiar', 'family', [])
        .then(chatId => setSelectedChatId(chatId))
        .catch(console.error);
    }
  }, [chatRooms, selectedChatId, currentUser, createChatRoom]);

  // Handle send message
  const handleSendMessage = async () => {
    if (!selectedChatId || !newMessage.trim()) return;

    try {
      await sendMessage(selectedChatId, newMessage.trim());
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle section change
  const handleSectionChange = (section: DashboardSection) => {
    setActiveSection(section);
    switch (section) {
      case DashboardSection.HOME:
        navigate('/adult-dashboard');
        break;
      case DashboardSection.TASKS:
        navigate('/parent-tasks');
        break;
      case DashboardSection.CHAT:
        break;
      default:
        break;
    }
  };

  // Format message time
  const formatMessageTime = (timestamp: any) => {
    if (!timestamp) return '';

    let date: Date;
    if (timestamp.toDate) {
      date = timestamp.toDate();
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else {
      return '';
    }

    return date.toLocaleTimeString('pt-PT', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!currentUser) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '100vh' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="adult-chat-page">
      {/* Desktop Layout */}
      <div className="d-none d-lg-flex h-100">
        <div className="row g-0 w-100">
          {/* Sidebar */}
          <div className="col-4 chat-sidebar">
            <div className="chat-sidebar-header">
              <div className="d-flex align-items-center justify-content-between p-3">
                <div>
                  <h5 className="mb-0 text-white">
                    <i className="fas fa-comments me-2"></i>
                    Conversas
                  </h5>
                  <small className="text-white-50">
                    {userProfile?.displayName}
                  </small>
                </div>
                <button
                  className="btn btn-outline-light btn-sm"
                  onClick={() => createChatRoom('Nova Conversa', 'private', [])}
                  title="Nova conversa"
                >
                  <i className="fas fa-plus"></i>
                </button>
              </div>
            </div>

            <div className="chat-list">
              {chatRooms.length === 0 ? (
                <div className="text-center p-4 text-muted">
                  <i className="fas fa-comments fa-2x mb-2"></i>
                  <p>Nenhuma conversa ainda</p>
                </div>
              ) : (
                chatRooms.map(room => (
                  <div
                    key={room.id}
                    className={`chat-item ${selectedChatId === room.id ? 'active' : ''}`}
                    onClick={() => setSelectedChatId(room.id)}
                  >
                    <div className="chat-avatar">
                      {room.type === 'family' ? '👨‍👩‍👧‍👦' : '💬'}
                    </div>
                    <div className="chat-info">
                      <div className="chat-name">{room.name}</div>
                      <div className="chat-last-message">
                        {room.lastMessage?.content || 'Nenhuma mensagem'}
                      </div>
                    </div>
                    <div className="chat-meta">
                      <div className="chat-time">
                        {room.lastMessage?.timestamp && formatMessageTime(room.lastMessage.timestamp)}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Main Chat Area */}
          <div className="col-8 chat-main">
            {selectedChatId && currentChatRoom ? (
              <>
                {/* Chat Header */}
                <div className="chat-header">
                  <div className="d-flex align-items-center">
                    <div className="chat-avatar me-3">
                      {currentChatRoom.type === 'family' ? '👨‍👩‍👧‍👦' : '💬'}
                    </div>
                    <div>
                      <h6 className="mb-0 text-white">{currentChatRoom.name}</h6>
                      <small className="text-white-50">
                        {currentChatRoom.participants.length} participantes
                      </small>
                    </div>
                  </div>
                  <div className="chat-actions">
                    <button className="btn btn-outline-light btn-sm me-2">
                      <i className="fas fa-phone"></i>
                    </button>
                    <button className="btn btn-outline-light btn-sm">
                      <i className="fas fa-video"></i>
                    </button>
                  </div>
                </div>

                {/* Messages Area */}
                <div className="chat-messages">
                  {currentMessages.length === 0 ? (
                    <div className="text-center p-4 text-muted">
                      <i className="fas fa-comment-dots fa-2x mb-2"></i>
                      <p>Nenhuma mensagem ainda</p>
                      <p className="small">Seja o primeiro a enviar uma mensagem!</p>
                    </div>
                  ) : (
                    currentMessages.map(message => (
                      <div
                        key={message.id}
                        className={`message ${message.senderId === currentUser.uid ? 'own' : 'other'}`}
                      >
                        <div className="message-content">
                          <div className="message-bubble">
                            <div className="message-text">{message.content}</div>
                            <div className="message-time">
                              {formatMessageTime(message.timestamp)}
                            </div>
                          </div>
                          <div className="message-sender">
                            {message.senderName} {message.senderAvatar}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* Message Input */}
                <div className="chat-input">
                  <div className="input-group">
                    <button className="btn btn-outline-secondary">
                      <i className="fas fa-smile"></i>
                    </button>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Escreva uma mensagem..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={handleKeyPress}
                    />
                    <button className="btn btn-outline-secondary">
                      <i className="fas fa-paperclip"></i>
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={handleSendMessage}
                      disabled={!newMessage.trim()}
                    >
                      <i className="fas fa-paper-plane"></i>
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <div className="chat-empty">
                <div className="text-center">
                  <i className="fas fa-comments fa-4x text-muted mb-3"></i>
                  <h4 className="text-muted">Selecione uma conversa</h4>
                  <p className="text-muted">Escolha uma conversa para começar a enviar mensagens</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="d-lg-none mobile-chat">
        {!showMobileChat ? (
          <>
            <div className="mobile-header">
              <div className="d-flex align-items-center justify-content-between p-3">
                <div>
                  <h5 className="mb-0 text-white">
                    <i className="fas fa-comments me-2"></i>
                    Conversas
                  </h5>
                  <small className="text-white-50">{userProfile?.displayName}</small>
                </div>
                <button
                  className="btn btn-outline-light btn-sm"
                  onClick={() => createChatRoom('Nova Conversa', 'private', [])}
                >
                  <i className="fas fa-plus"></i>
                </button>
              </div>
            </div>

            <div className="mobile-chat-list">
              {chatRooms.map(room => (
                <div
                  key={room.id}
                  className="mobile-chat-item"
                  onClick={() => {
                    setSelectedChatId(room.id);
                    setShowMobileChat(true);
                  }}
                >
                  <div className="chat-avatar">
                    {room.type === 'family' ? '👨‍👩‍👧‍👦' : '💬'}
                  </div>
                  <div className="chat-info">
                    <div className="chat-name">{room.name}</div>
                    <div className="chat-last-message">
                      {room.lastMessage?.content || 'Nenhuma mensagem'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="mobile-chat-conversation">
            <div className="mobile-chat-header">
              <div className="d-flex align-items-center">
                <button
                  className="btn btn-link text-white me-2 p-0"
                  onClick={() => setShowMobileChat(false)}
                >
                  <i className="fas fa-arrow-left"></i>
                </button>
                <div className="chat-avatar me-3">
                  {currentChatRoom?.type === 'family' ? '👨‍👩‍👧‍👦' : '💬'}
                </div>
                <div>
                  <h6 className="mb-0 text-white">{currentChatRoom?.name}</h6>
                  <small className="text-white-50">Online</small>
                </div>
              </div>
            </div>

            <div className="mobile-messages">
              {currentMessages.map(message => (
                <div
                  key={message.id}
                  className={`mobile-message ${message.senderId === currentUser.uid ? 'own' : 'other'}`}
                >
                  <div className="message-bubble">
                    <div className="message-text">{message.content}</div>
                    <div className="message-time">
                      {formatMessageTime(message.timestamp)}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mobile-input">
              <div className="input-group">
                <input
                  type="text"
                  className="form-control"
                  placeholder="Mensagem..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyDown={handleKeyPress}
                />
                <button
                  className="btn btn-primary"
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                >
                  <i className="fas fa-paper-plane"></i>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Mobile Navigation */}
      <MobileNavbar
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
        unreadCount={0}
      />
    </div>
  );
};

export default ChatPage;