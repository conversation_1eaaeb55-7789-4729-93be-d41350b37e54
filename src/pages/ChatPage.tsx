import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useChat } from '../contexts/ChatContext';
import MobileNavbar from '../components/MobileNavbar';
import ChatList from '../components/ChatList';
import ChatWindow from '../components/ChatWindow';
import { DashboardSection } from '../types/enums';

/**
 * ChatPage component - Real-time chat interface with Firebase
 */
const ChatPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser, userProfile } = useAuth();
  const {
    chatRooms,
    messages,
    typingUsers,
    createChatRoom,
  } = useChat();

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [showMobileChat, setShowMobileChat] = useState(false);
  const [activeSection, setActiveSection] = useState<DashboardSection>(DashboardSection.CHAT);

  // Get current chat room
  const currentChatRoom = selectedChatId ? chatRooms.find(room => room.id === selectedChatId) : null;
  const currentMessages = selectedChatId ? messages[selectedChatId] || [] : [];
  const currentTypingUsers = selectedChatId ? typingUsers[selectedChatId] || [] : [];

  // Handle chat selection
  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId);
    setShowMobileChat(true);
  };

  // Handle new chat creation
  const handleNewChat = async () => {
    try {
      // For demo, create a family chat
      const chatId = await createChatRoom('Chat Familiar', 'family', []);
      setSelectedChatId(chatId);
    } catch (error) {
      console.error('Error creating chat:', error);
    }
  };

  // Handle mobile back
  const handleMobileBack = () => {
    setSelectedChatId(null);
    setShowMobileChat(false);
  };

  // Handle section change for mobile navigation
  const handleSectionChange = (section: DashboardSection) => {
    setActiveSection(section);

    switch (section) {
      case DashboardSection.HOME:
        navigate('/adult-dashboard');
        break;
      case DashboardSection.TASKS:
        navigate('/parent-tasks');
        break;
      case DashboardSection.CHAT:
        // Stay on chat page
        break;
      default:
        break;
    }
  };

  // Initialize default family chat if none exists
  useEffect(() => {
    if (currentUser && chatRooms.length === 0) {
      // Auto-create family chat if none exists
      createChatRoom('Chat Familiar', 'family', []).catch(console.error);
    }
  }, [currentUser, chatRooms, createChatRoom]);

  if (!currentUser) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '100vh' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="chat-page">
      {/* Desktop Layout */}
      <div className="d-none d-lg-flex chat-desktop-layout">
        <div className="row g-0 h-100">
          {/* Chat List Sidebar */}
          <div className="col-4 chat-sidebar">
            <div className="chat-sidebar-content">
              <div className="chat-header-section">
                <div className="d-flex align-items-center justify-content-between p-3">
                  <div>
                    <h5 className="mb-0">
                      <i className="fas fa-comments me-2"></i>
                      Chat
                    </h5>
                    <small className="text-muted">
                      Olá, {userProfile?.displayName || 'Utilizador'}!
                    </small>
                  </div>
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={handleNewChat}
                    title="Nova conversa"
                  >
                    <i className="fas fa-plus"></i>
                  </button>
                </div>
              </div>

              <ChatList
                chatRooms={chatRooms}
                currentChatId={selectedChatId}
                onChatSelect={handleChatSelect}
                onNewChat={handleNewChat}
                showNewChatButton={false}
              />
            </div>
          </div>

          {/* Chat Window Area */}
          <div className="col-8 chat-main">
            <ChatWindow
              chatRoom={currentChatRoom}
              messages={currentMessages}
              typingUsers={currentTypingUsers}
            />
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="d-lg-none chat-mobile-layout">
        {!showMobileChat ? (
          <div className="mobile-chat-list">
            <div className="mobile-header p-3">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <h5 className="mb-0">
                    <i className="fas fa-comments me-2"></i>
                    Chat
                  </h5>
                  <small className="text-muted">
                    {userProfile?.displayName || 'Utilizador'}
                  </small>
                </div>
                <button
                  className="btn btn-primary btn-sm"
                  onClick={handleNewChat}
                >
                  <i className="fas fa-plus"></i>
                </button>
              </div>
            </div>

            <ChatList
              chatRooms={chatRooms}
              currentChatId={selectedChatId}
              onChatSelect={handleChatSelect}
              onNewChat={handleNewChat}
              showNewChatButton={false}
            />
          </div>
        ) : (
          <div className="mobile-chat-conversation">
            <ChatWindow
              chatRoom={currentChatRoom}
              messages={currentMessages}
              typingUsers={currentTypingUsers}
              onBack={handleMobileBack}
              showBackButton={true}
            />
          </div>
        )}
      </div>

      {/* Mobile Bottom Navbar */}
      <MobileNavbar
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
        unreadCount={chatRooms.reduce((total, room) =>
          total + (room.unreadCounts?.[currentUser.uid] || 0), 0
        )}
      />
    </div>
  );
};

export default ChatPage;