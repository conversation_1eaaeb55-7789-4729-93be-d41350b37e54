import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Task } from '../types/schema';
import { TaskStatus, ChildDashboardSection } from '../types/enums';
import { mockTasks } from '../data/TaskMockData';
import ChildMobileNavbar from '../components/ChildMobileNavbar';

/**
 * ChildCalendarPage component - Child-friendly calendar view
 * Shows tasks and activities in a colorful, engaging calendar format
 */
const ChildCalendarPage: React.FC = () => {
  const navigate = useNavigate();
  
  // Mock current child user
  const currentChild = {
    id: 'child-123',
    name: '<PERSON>',
    avatar: '👦'
  };

  // State management
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());

  // Filter tasks for current child
  const childTasks = useMemo(() => {
    return mockTasks.filter(task => 
      task.childId === currentChild.id || task.childId === 'child-1'
    );
  }, [currentChild.id]);

  // Get calendar data for current month
  const calendarData = useMemo(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // Get first day of month and number of days
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay()); // Start from Sunday
    
    const days = [];
    for (let i = 0; i < 42; i++) { // 6 weeks * 7 days
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      days.push(date);
    }
    
    return days;
  }, [currentDate]);

  // Helper functions
  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth();
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const formatMonthYear = (date: Date) => {
    return date.toLocaleDateString('pt-PT', { 
      month: 'long', 
      year: 'numeric' 
    });
  };

  // Get tasks for a specific date
  const getTasksForDate = (date: Date) => {
    return childTasks.filter(task => {
      const taskDate = new Date(task.dueDate);
      return taskDate.toDateString() === date.toDateString();
    });
  };

  // Get tasks for selected date
  const selectedDateTasks = useMemo(() => {
    if (!selectedDate) return [];
    return getTasksForDate(selectedDate);
  }, [selectedDate, childTasks]);

  // Navigation functions
  const goToPreviousMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
  };

  const goToToday = () => {
    const today = new Date();
    setCurrentDate(today);
    setSelectedDate(today);
  };

  // Event handlers
  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
  };

  const getTaskStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.COMPLETED:
        return '#4ECDC4';
      case TaskStatus.IN_PROGRESS:
        return '#FFD93D';
      case TaskStatus.PENDING:
        return '#FF6B6B';
      default:
        return '#96CEB4';
    }
  };

  const getTaskStatusEmoji = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.COMPLETED:
        return '✅';
      case TaskStatus.IN_PROGRESS:
        return '⏳';
      case TaskStatus.PENDING:
        return '📝';
      default:
        return '📋';
    }
  };

  return (
    <div className="child-calendar-page">
      {/* Header */}
      <div className="child-calendar-header">
        <div className="container-fluid px-3 py-4">
          <div className="d-flex align-items-center justify-content-between">
            <div className="d-flex align-items-center">
              <button 
                className="child-back-btn me-3"
                onClick={() => navigate('/child-dashboard')}
              >
                <i className="fas fa-arrow-left"></i>
              </button>
              <div>
                <h2 className="child-page-title mb-1">
                  📅 O Meu Calendário
                </h2>
                <p className="child-page-subtitle mb-0">
                  Vê as tuas tarefas e atividades!
                </p>
              </div>
            </div>
            <div className="calendar-today-btn">
              <button className="btn-today" onClick={goToToday}>
                <i className="fas fa-calendar-day"></i>
                <span>Hoje</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Calendar Content */}
      <div className="child-calendar-content">
        <div className="container-fluid px-3">
          
          {/* Month Navigation */}
          <div className="month-navigation">
            <button className="nav-btn prev" onClick={goToPreviousMonth}>
              <i className="fas fa-chevron-left"></i>
            </button>
            <h3 className="month-title">
              {formatMonthYear(currentDate)}
            </h3>
            <button className="nav-btn next" onClick={goToNextMonth}>
              <i className="fas fa-chevron-right"></i>
            </button>
          </div>

          {/* Calendar Grid */}
          <div className="calendar-container">
            {/* Weekday Headers */}
            <div className="calendar-weekdays">
              {['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'].map(day => (
                <div key={day} className="weekday-header">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Days */}
            <div className="calendar-days">
              {calendarData.map((date, index) => {
                const tasksForDay = getTasksForDate(date);
                const isCurrentMonthDay = isCurrentMonth(date);
                const isTodayDay = isToday(date);
                const isSelectedDay = selectedDate && date.toDateString() === selectedDate.toDateString();

                return (
                  <div
                    key={index}
                    className={`calendar-day ${!isCurrentMonthDay ? 'other-month' : ''} ${isTodayDay ? 'today' : ''} ${isSelectedDay ? 'selected' : ''}`}
                    onClick={() => handleDateClick(date)}
                  >
                    <div className="day-number">
                      {date.getDate()}
                    </div>
                    
                    {/* Task indicators */}
                    {tasksForDay.length > 0 && (
                      <div className="task-indicators">
                        {tasksForDay.slice(0, 3).map((task, taskIndex) => (
                          <div
                            key={taskIndex}
                            className="task-dot"
                            style={{ backgroundColor: getTaskStatusColor(task.status) }}
                            title={task.title}
                          />
                        ))}
                        {tasksForDay.length > 3 && (
                          <div className="more-tasks">
                            +{tasksForDay.length - 3}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Selected Date Tasks */}
          {selectedDate && (
            <div className="selected-date-section">
              <h4 className="selected-date-title">
                📋 Tarefas para {selectedDate.toLocaleDateString('pt-PT', { 
                  weekday: 'long', 
                  day: 'numeric', 
                  month: 'long' 
                })}
              </h4>
              
              {selectedDateTasks.length === 0 ? (
                <div className="no-tasks">
                  <div className="no-tasks-icon">😎</div>
                  <h5>Nenhuma tarefa para hoje!</h5>
                  <p>Aproveita o dia livre!</p>
                </div>
              ) : (
                <div className="tasks-list">
                  {selectedDateTasks.map(task => (
                    <div key={task.id} className="task-card">
                      <div className="task-status-icon">
                        {getTaskStatusEmoji(task.status)}
                      </div>
                      <div className="task-content">
                        <h6 className="task-title">{task.title}</h6>
                        <p className="task-description">{task.description}</p>
                        <div className="task-meta">
                          <span className="task-points">
                            <i className="fas fa-star"></i>
                            {task.points} pontos
                          </span>
                          <span className="task-time">
                            <i className="fas fa-clock"></i>
                            {task.estimatedDuration} min
                          </span>
                        </div>
                      </div>
                      <div className="task-action">
                        {task.status === TaskStatus.COMPLETED ? (
                          <div className="completed-badge">
                            <i className="fas fa-check"></i>
                            Feito!
                          </div>
                        ) : (
                          <button 
                            className="start-task-btn"
                            onClick={() => navigate('/child-tasks')}
                          >
                            <i className="fas fa-play"></i>
                            Fazer
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Mobile Navigation */}
      <ChildMobileNavbar
        activeSection={ChildDashboardSection.CALENDAR}
        onSectionChange={(section) => {
          if (section === ChildDashboardSection.DASHBOARD) navigate('/child-dashboard');
          if (section === ChildDashboardSection.TASKS) navigate('/child-tasks');
          if (section === ChildDashboardSection.CHAT) navigate('/child-chat-adult-style');
          if (section === ChildDashboardSection.REWARDS) navigate('/child-rewards');
          if (section === ChildDashboardSection.TROPHIES) navigate('/child-trophies');
        }}
        unreadCount={0}
      />
    </div>
  );
};

export default ChildCalendarPage;
