import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useChat } from '../contexts/ChatContext';
import ChildMobileNavbar from '../components/ChildMobileNavbar';
import ChatList from '../components/ChatList';
import ChatWindow from '../components/ChatWindow';
import { ChildDashboardSection, OnlineStatus, MessageStatus, ChatStatus, ChatType, MessageType } from '../types/enums';
import { Chat, FamilyMember } from '../types/schema';

/**
 * ChildChatPageAdultStyle component - Child chat with adult-style interface
 */
const ChildChatPageAdultStyle: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser, userProfile } = useAuth();
  const {
    chatRooms,
    messages,
    typingUsers,
    createChatRoom,
  } = useChat();

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [showMobileChat, setShowMobileChat] = useState(false);
  const [activeSection, setActiveSection] = useState<ChildDashboardSection>(ChildDashboardSection.CHAT);

  // Get current chat room
  const currentChatRoom = selectedChatId ? chatRooms.find(room => room.id === selectedChatId) : null;
  const currentMessages = selectedChatId ? messages[selectedChatId] || [] : [];
  const currentTypingUsers = selectedChatId ? typingUsers[selectedChatId] || [] : [];

  // Handle chat selection
  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId);
    setShowMobileChat(true);
  };

  // Handle new chat creation
  const handleNewChat = async () => {
    try {
      // For demo, create a family chat
      const chatId = await createChatRoom('Chat com os Pais', 'family', []);
      setSelectedChatId(chatId);
    } catch (error) {
      console.error('Error creating chat:', error);
    }
  };

  // Handle mobile back
  const handleMobileBack = () => {
    setSelectedChatId(null);
    setShowMobileChat(false);
  };

  // Handle section change for mobile navigation
  const handleSectionChange = (section: ChildDashboardSection) => {
    setActiveSection(section);

    switch (section) {
      case ChildDashboardSection.DASHBOARD:
        navigate('/child-dashboard');
        break;
      case ChildDashboardSection.TASKS:
        navigate('/child-tasks');
        break;
      case ChildDashboardSection.CHAT:
        // Stay on chat page
        break;
      case ChildDashboardSection.REWARDS:
        navigate('/child-rewards');
        break;
      case ChildDashboardSection.TROPHIES:
        navigate('/child-trophies');
        break;
      case ChildDashboardSection.CALENDAR:
        navigate('/child-calendar');
        break;
      default:
        break;
    }
  };

  // Initialize default family chat if none exists
  useEffect(() => {
    if (currentUser && chatRooms.length === 0) {
      // Auto-create family chat if none exists
      createChatRoom('Chat com os Pais', 'family', []).catch(console.error);
    }
  }, [currentUser, chatRooms, createChatRoom]);

  if (!currentUser) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '100vh' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="child-chat-page">
      {/* Desktop Layout */}
      <div className="d-none d-lg-flex chat-desktop-layout">
        <div className="row g-0 h-100">
          {/* Chat List Sidebar */}
          <div className="col-4 chat-sidebar">
            <div className="chat-sidebar-content">
              <div className="chat-header-section">
                <div className="d-flex align-items-center justify-content-between p-3">
                  <div>
                    <h5 className="mb-0">
                      <i className="fas fa-comments me-2"></i>
                      Chat
                    </h5>
                    <small className="text-muted">
                      Olá, {userProfile?.displayName || 'Criança'}! 👋
                    </small>
                  </div>
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={handleNewChat}
                    title="Nova conversa"
                  >
                    <i className="fas fa-plus"></i>
                  </button>
                </div>
              </div>

              <ChatList
                chatRooms={chatRooms}
                currentChatId={selectedChatId}
                onChatSelect={handleChatSelect}
                onNewChat={handleNewChat}
                showNewChatButton={false}
              />
            </div>
          </div>

          {/* Chat Window Area */}
          <div className="col-8 chat-main">
            <ChatWindow
              chatRoom={currentChatRoom}
              messages={currentMessages}
              typingUsers={currentTypingUsers}
            />
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="d-lg-none chat-mobile-layout">
        {!showMobileChat ? (
          <div className="mobile-chat-list">
            <div className="mobile-header p-3">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <h5 className="mb-0">
                    <i className="fas fa-comments me-2"></i>
                    Chat
                  </h5>
                  <small className="text-muted">
                    {userProfile?.displayName || 'Criança'} 👋
                  </small>
                </div>
                <button
                  className="btn btn-primary btn-sm"
                  onClick={handleNewChat}
                >
                  <i className="fas fa-plus"></i>
                </button>
              </div>
            </div>

            <ChatList
              chatRooms={chatRooms}
              currentChatId={selectedChatId}
              onChatSelect={handleChatSelect}
              onNewChat={handleNewChat}
              showNewChatButton={false}
            />
          </div>
        ) : (
          <div className="mobile-chat-conversation">
            <ChatWindow
              chatRoom={currentChatRoom}
              messages={currentMessages}
              typingUsers={currentTypingUsers}
              onBack={handleMobileBack}
              showBackButton={true}
            />
          </div>
        )}
      </div>

      {/* Mobile Bottom Navbar */}
      <ChildMobileNavbar
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
        unreadCount={chatRooms.reduce((total, room) =>
          total + (room.unreadCounts?.[currentUser.uid] || 0), 0
        )}
      />
    </div>
  );
};

export default ChildChatPageAdultStyle;

// Mock data específico para crianças com visual de adultos
const mockChildUser: FamilyMember = {
  id: 'child-123',
  name: 'João',
  avatar: '👦',
  onlineStatus: OnlineStatus.ONLINE,
  lastSeen: new Date()
};

const mockFamilyMembers: FamilyMember[] = [
  mockChildUser,
  {
    id: 'parent-1',
    name: 'Mãe',
    avatar: '👩',
    onlineStatus: OnlineStatus.ONLINE,
    lastSeen: new Date()
  },
  {
    id: 'parent-2',
    name: 'Pai',
    avatar: '👨',
    onlineStatus: OnlineStatus.OFFLINE,
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
  },
  {
    id: 'sibling-1',
    name: 'Ana',
    avatar: '👧',
    onlineStatus: OnlineStatus.ONLINE,
    lastSeen: new Date()
  }
];

const mockChildChatsAdultStyle: Chat[] = [
  {
    id: 'sebastian-chat',
    name: 'Sebastian Rudiger',
    type: ChatType.PRIVATE,
    participants: [
      mockChildUser,
      {
        id: 'sebastian-rudiger',
        name: 'Sebastian Rudiger',
        avatar: '👨‍💼',
        onlineStatus: OnlineStatus.ONLINE,
        lastSeen: new Date()
      } as FamilyMember
    ],
    lastMessage: {
      id: 'msg-1',
      senderId: 'child-123',
      senderName: 'Jimmy',
      content: 'Perfect! Will check it 👍',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 min ago
      status: MessageStatus.READ,
    },
    unreadCount: 0,
    status: ChatStatus.ACTIVE,
    isPinned: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'family-chat-child-adult',
    name: 'Chat da Família',
    type: ChatType.FAMILY,
    participants: mockFamilyMembers,
    lastMessage: {
      id: 'msg-1',
      senderId: 'parent-1',
      senderName: 'Mãe',
      content: 'João, não te esqueças de arrumar o quarto! 😊',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 min ago
      status: MessageStatus.SENT,
    },
    unreadCount: 2,
    status: ChatStatus.ACTIVE,
    isPinned: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'chat-parent-1-adult',
    name: 'Mãe',
    type: ChatType.PRIVATE,
    participants: mockFamilyMembers.slice(0, 2),
    lastMessage: {
      id: 'msg-2',
      senderId: mockChildUser.id,
      senderName: mockChildUser.name,
      content: 'Já arrumei o quarto! 🎉',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 min ago
      status: MessageStatus.SENT,
    },
    unreadCount: 0,
    status: ChatStatus.ACTIVE,
    isPinned: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'chat-parent-2-adult',
    name: 'Pai',
    type: ChatType.PRIVATE,
    participants: mockFamilyMembers.slice(1, 3),
    lastMessage: {
      id: 'msg-3',
      senderId: 'parent-2',
      senderName: 'Pai',
      content: 'Bom trabalho no teste de matemática! ⭐',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      status: MessageStatus.SENT,
    },
    unreadCount: 0,
    status: ChatStatus.ACTIVE,
    isPinned: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  }
];
