import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import ChatHeader from '../components/ChatHeader';
import ChatFilterPills from '../components/ChatFilterPills';
import ChatItem from '../components/ChatItem';
import ChildiMessageConversation from '../components/ChildiMessageConversation';
import CreatePrivateChatModal from '../components/CreatePrivateChatModal';
import CallInterface from '../components/CallInterface';
import ChildMobileNavbar from '../components/ChildMobileNavbar';
import { Chat, FamilyMember, CallRecord } from '../types/schema';
import { ChatFilterType, ChatType, CallType, CallStatus, MessageType, MessageStatus, ChildDashboardSection, OnlineStatus, ChatStatus } from '../types/enums';
import { mockFamilyMembers } from '../data/ChatMockData';

// Mock data específico para crianças com visual de adultos
const mockChildUser = {
  id: 'child-123',
  name: '<PERSON>',
  avatar: '👦',
  onlineStatus: OnlineStatus.ONLINE,
  lastSeen: new Date()
};

const mockChildChatsAdultStyle: Chat[] = [
  {
    id: 'sebastian-chat',
    name: 'Sebastian Rudiger',
    type: ChatType.PRIVATE,
    participants: [
      mockChildUser,
      {
        id: 'sebastian-rudiger',
        name: 'Sebastian Rudiger',
        avatar: '👨‍💼',
        onlineStatus: OnlineStatus.ONLINE,
        lastSeen: new Date()
      }
    ],
    lastMessage: {
      id: 'msg-1',
      senderId: 'child-123',
      senderName: 'Jimmy',
      content: 'Perfect! Will check it 👍',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 min ago
      status: MessageStatus.READ,
    },
    unreadCount: 0,
    status: ChatStatus.ACTIVE,
    isPinned: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'family-chat-child-adult',
    name: 'Chat da Família',
    type: ChatType.FAMILY,
    participants: mockFamilyMembers,
    lastMessage: {
      id: 'msg-1',
      senderId: 'parent-1',
      senderName: 'Mãe',
      content: 'João, não te esqueças de arrumar o quarto! 😊',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 min ago
      status: MessageStatus.SENT,
    },
    unreadCount: 2,
    status: ChatStatus.ACTIVE,
    isPinned: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'chat-parent-1-adult',
    name: 'Mãe',
    type: ChatType.PRIVATE,
    participants: mockFamilyMembers.slice(0, 2),
    lastMessage: {
      id: 'msg-2',
      senderId: mockChildUser.id,
      senderName: mockChildUser.name,
      content: 'Já arrumei o quarto! 🎉',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 min ago
      status: MessageStatus.SENT,
    },
    unreadCount: 0,
    status: ChatStatus.ACTIVE,
    isPinned: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'chat-parent-2-adult',
    name: 'Pai',
    type: ChatType.PRIVATE,
    participants: mockFamilyMembers.slice(1, 3),
    lastMessage: {
      id: 'msg-3',
      senderId: 'parent-2',
      senderName: 'Pai',
      content: 'Bom trabalho no teste de matemática! ⭐',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      status: MessageStatus.SENT,
    },
    unreadCount: 0,
    status: ChatStatus.ACTIVE,
    isPinned: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  }
];

/**
 * ChildChatPageAdultStyle component - Chat interface for children using adult visual styling
 * Uses the same visual components as adult chat but with child-specific functionality and navigation
 */
const ChildChatPageAdultStyle: React.FC = () => {
  const navigate = useNavigate();
  const [activeFilter, setActiveFilter] = useState<ChatFilterType>(ChatFilterType.ALL);
  const [chats, setChats] = useState<Chat[]>(mockChildChatsAdultStyle);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [activeCall, setActiveCall] = useState<CallRecord | null>(null);
  const [isCallActive, setIsCallActive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [activeSection, setActiveSection] = useState<ChildDashboardSection>(ChildDashboardSection.FAMILY_CHAT);

  const currentUser = mockChildUser;

  // Filter chats based on active filter
  const getFilteredChats = useCallback((): Chat[] => {
    let filteredChats = [...chats];

    switch (activeFilter) {
      case ChatFilterType.UNREAD:
        filteredChats = filteredChats.filter(chat => chat.unreadCount > 0);
        break;
      case ChatFilterType.RECEIVED:
        filteredChats = filteredChats.filter(chat => 
          chat.lastMessage && chat.lastMessage.senderId !== currentUser.id
        );
        break;
      case ChatFilterType.ARCHIVED:
        filteredChats = filteredChats.filter(chat => chat.status === 'archived');
        break;
      case ChatFilterType.ALL:
      default:
        filteredChats = filteredChats.filter(chat => chat.status !== 'archived');
        break;
    }

    // Sort by pinned status and last message time
    return filteredChats.sort((a, b) => {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      
      const aTime = a.lastMessage?.timestamp.getTime() || 0;
      const bTime = b.lastMessage?.timestamp.getTime() || 0;
      return bTime - aTime;
    });
  }, [chats, activeFilter, currentUser.id]);

  const handleChatSelect = (chatId: string) => {
    setSelectedChat(chatId);
    
    // Mark messages as read
    setChats(prevChats => 
      prevChats.map(chat => 
        chat.id === chatId 
          ? { ...chat, unreadCount: 0 }
          : chat
      )
    );
    console.log('Selected chat:', chatId);
  };

  const handleBackToList = () => {
    setSelectedChat(null);
  };

  const handleSwipeAction = (chatId: string, action: string) => {
    setChats(prevChats => 
      prevChats.map(chat => {
        if (chat.id !== chatId) return chat;

        switch (action) {
          case 'archive':
            return { ...chat, status: 'archived' as const };
          case 'unarchive':
            return { ...chat, status: 'active' as const };
          case 'mark-read':
            return { ...chat, unreadCount: 0 };
          case 'mark-unread':
            return { ...chat, unreadCount: Math.max(1, chat.unreadCount) };
          case 'mute':
            return { ...chat, status: 'muted' as const };
          case 'unmute':
            return { ...chat, status: 'active' as const };
          case 'delete':
            // Only allow deleting private chats
            if (chat.type === ChatType.PRIVATE) {
              return null;
            }
            return chat;
          default:
            return chat;
        }
      }).filter(Boolean) as Chat[]
    );

    console.log(`Action ${action} performed on chat ${chatId}`);
  };

  const handleCreatePrivateChat = (participantId: string) => {
    const participant = mockFamilyMembers.find(m => m.id === participantId);
    if (!participant) return;

    const newChat: Chat = {
      id: `private-chat-${Date.now()}`,
      type: ChatType.PRIVATE,
      name: participant.name,
      participants: [currentUser, participant],
      unreadCount: 0,
      status: 'active',
      isPinned: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setChats(prevChats => [...prevChats, newChat]);
    console.log('Created private chat with:', participant.name);
  };

  const handleStartCall = (type: CallType) => {
    const currentChat = chats.find(chat => chat.id === selectedChat);
    const participants = currentChat ? currentChat.participants : mockFamilyMembers;

    const newCall: CallRecord = {
      id: `call-${Date.now()}`,
      type,
      status: CallStatus.OUTGOING,
      participants,
      duration: 0,
      timestamp: new Date(),
      initiatorId: currentUser.id
    };

    setActiveCall(newCall);
    setIsCallActive(true);

    // Simulate call connecting
    setTimeout(() => {
      setActiveCall(prev => prev ? { ...prev, status: CallStatus.ACTIVE } : null);
    }, 3000);

    console.log('Started call:', type);
  };

  const handleEndCall = () => {
    setActiveCall(null);
    setIsCallActive(false);
    console.log('Call ended');
  };

  const handleToggleVideo = () => {
    console.log('Toggle video');
  };

  const handleToggleMute = () => {
    console.log('Toggle mute');
  };

  const handleShowCallHistory = () => {
    console.log('Show call history');
    // In a real app, this would open a call history modal
  };

  const handleSectionChange = (section: ChildDashboardSection) => {
    setActiveSection(section);

    switch (section) {
      case ChildDashboardSection.HOME:
        navigate('/child-dashboard');
        break;
      case ChildDashboardSection.MY_TASKS:
        navigate('/child-tasks');
        break;
      case ChildDashboardSection.MY_REWARDS:
        navigate('/child-rewards');
        break;
      case ChildDashboardSection.FAMILY_CHAT:
        // Stay on chat page
        break;
      default:
        break;
    }
  };

  // Infinite scroll handler
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (scrollHeight - scrollTop === clientHeight && hasMore && !loading) {
      setLoading(true);

      // Simulate loading more chats
      setTimeout(() => {
        setLoading(false);
        // In a real app, you would load more chats here
        console.log('Load more chats...');
      }, 1000);
    }
  }, [hasMore, loading]);

  const filteredChats = getFilteredChats();
  const unreadCount = chats.filter(chat => chat.unreadCount > 0).length;
  const archivedCount = chats.filter(chat => chat.status === 'archived').length;
  const currentChat = chats.find(chat => chat.id === selectedChat);

  // Desktop layout with chat list and conversation side by side
  return (
    <div className="modern-chat-app">
      {/* Mobile view - show only conversation if chat selected */}
      {selectedChat && currentChat && (
        <div className="mobile-conversation-view d-md-none">
          <ChildiMessageConversation
            chat={currentChat}
            currentUser={currentUser}
            onBack={handleBackToList}
            onStartCall={handleStartCall}
          />



          <CallInterface
            call={activeCall}
            isActive={isCallActive}
            onEndCall={handleEndCall}
            onToggleVideo={handleToggleVideo}
            onToggleMute={handleToggleMute}
          />
        </div>
      )}

      {/* Desktop layout - always show both panels */}
      <div className={`desktop-chat-layout d-none d-md-flex ${selectedChat ? '' : 'd-flex'}`}>
        {/* Left Panel - Chat List */}
        <div className="chat-list-panel">
          <div className="chat-list-header">
            <div className="d-flex align-items-center justify-content-between p-3">
              <h5 className="mb-0 fw-bold">Mensagens</h5>
              <button
                className="btn btn-link p-0"
                onClick={() => navigate('/child-dashboard')}
                title="Voltar ao Dashboard"
              >
                <i className="fas fa-times fa-lg text-muted"></i>
              </button>
            </div>

            {/* Search bar */}
            <div className="search-container p-3 pt-0">
              <div className="search-input-wrapper">
                <i className="fas fa-search search-icon"></i>
                <input
                  type="text"
                  className="search-input"
                  placeholder="Procurar mensagem..."
                />
                <button className="search-edit-btn">
                  <i className="fas fa-edit"></i>
                </button>
              </div>
            </div>
          </div>

          {/* Chat List */}
          <div className="chat-list-container">
            {filteredChats.map(chat => (
              <div
                key={chat.id}
                className={`modern-chat-item ${selectedChat === chat.id ? 'active' : ''}`}
                onClick={() => handleChatSelect(chat.id)}
              >
                <div className="chat-item-avatar">
                  <img
                    src={`https://ui-avatars.com/api/?name=${encodeURIComponent(chat.name)}&background=6366f1&color=fff&size=48`}
                    alt={chat.name}
                    className="avatar-img"
                  />
                  <div className="online-indicator"></div>
                </div>

                <div className="chat-item-content">
                  <div className="chat-item-header">
                    <h6 className="chat-item-name">{chat.name}</h6>
                    <span className="chat-item-time">
                      {chat.lastMessage ?
                        chat.lastMessage.timestamp.toLocaleTimeString('pt-PT', {
                          hour: '2-digit',
                          minute: '2-digit'
                        }) : ''
                      }
                    </span>
                  </div>

                  <div className="chat-item-preview">
                    <span className="preview-text">
                      {chat.lastMessage?.content || 'Sem mensagens'}
                    </span>
                    {chat.unreadCount > 0 && (
                      <span className="unread-badge">{chat.unreadCount}</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Add chat button */}
          <div className="add-chat-container">
            <button
              className="add-chat-btn"
              onClick={() => setShowCreateModal(true)}
            >
              <i className="fas fa-plus"></i>
            </button>
          </div>
        </div>

        {/* Right Panel - Conversation */}
        <div className="conversation-panel">
          {selectedChat && currentChat ? (
            <ChildiMessageConversation
              chat={currentChat}
              currentUser={currentUser}
              onBack={handleBackToList}
              onStartCall={handleStartCall}
            />
          ) : (
            <div className="no-chat-selected">
              <div className="no-chat-content">
                <i className="fas fa-comments fa-3x text-muted mb-3"></i>
                <h5 className="text-muted">Seleciona uma conversa</h5>
                <p className="text-muted">Escolhe uma conversa da lista para começar a falar!</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Mobile view - show only chat list if no chat selected */}
      {!selectedChat && (
        <div className="mobile-chat-list d-md-none">
          <div className="mobile-header">
            <div className="d-flex align-items-center justify-content-between p-3">
              <h5 className="mb-0 fw-bold">Chat da Família</h5>
              <button
                className="btn btn-link p-0"
                onClick={() => navigate('/child-dashboard')}
              >
                <i className="fas fa-arrow-left fa-lg"></i>
              </button>
            </div>
          </div>

          <div className="mobile-chat-items">
            {filteredChats.map(chat => (
              <div
                key={chat.id}
                className="modern-chat-item mobile"
                onClick={() => handleChatSelect(chat.id)}
              >
                <div className="chat-item-avatar">
                  <img
                    src={`https://ui-avatars.com/api/?name=${encodeURIComponent(chat.name)}&background=6366f1&color=fff&size=48`}
                    alt={chat.name}
                    className="avatar-img"
                  />
                  <div className="online-indicator"></div>
                </div>

                <div className="chat-item-content">
                  <div className="chat-item-header">
                    <h6 className="chat-item-name">{chat.name}</h6>
                    <span className="chat-item-time">
                      {chat.lastMessage ?
                        chat.lastMessage.timestamp.toLocaleTimeString('pt-PT', {
                          hour: '2-digit',
                          minute: '2-digit'
                        }) : ''
                      }
                    </span>
                  </div>

                  <div className="chat-item-preview">
                    <span className="preview-text">
                      {chat.lastMessage?.content || 'Sem mensagens'}
                    </span>
                    {chat.unreadCount > 0 && (
                      <span className="unread-badge">{chat.unreadCount}</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <ChildMobileNavbar
            activeSection={activeSection}
            onSectionChange={handleSectionChange}
            unreadCount={unreadCount}
          />
        </div>
      )}

      {/* Modals and overlays */}
      <CreatePrivateChatModal
        show={showCreateModal}
        onHide={() => setShowCreateModal(false)}
        familyMembers={mockFamilyMembers}
        existingChats={chats}
        currentUser={currentUser}
        onCreateChat={handleCreatePrivateChat}
      />

      <CallInterface
        call={activeCall}
        isActive={isCallActive}
        onEndCall={handleEndCall}
        onToggleVideo={handleToggleVideo}
        onToggleMute={handleToggleMute}
      />
    </div>
  );
};

export default ChildChatPageAdultStyle;
