import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import ChatHeader from '../components/ChatHeader';
import ChatFilterPills from '../components/ChatFilterPills';
import ChatItem from '../components/ChatItem';
import ChildiMessageConversation from '../components/ChildiMessageConversation';
import CreatePrivateChatModal from '../components/CreatePrivateChatModal';
import CallInterface from '../components/CallInterface';
import ChildMobileNavbar from '../components/ChildMobileNavbar';
import { Chat, FamilyMember, CallRecord } from '../types/schema';
import { ChatFilterType, ChatType, CallType, CallStatus, MessageType, MessageStatus, ChildDashboardSection, OnlineStatus, ChatStatus } from '../types/enums';
import { mockFamilyMembers } from '../data/ChatMockData';

// Mock data específico para crianças com visual de adultos
const mockChildUser = {
  id: 'child-123',
  name: '<PERSON>',
  avatar: '👦',
  onlineStatus: OnlineStatus.ONLINE,
  lastSeen: new Date()
};

const mockChildChatsAdultStyle: Chat[] = [
  {
    id: 'family-chat-child-adult',
    name: 'Chat da Família',
    type: ChatType.FAMILY,
    participants: mockFamilyMembers,
    lastMessage: {
      id: 'msg-1',
      senderId: 'parent-1',
      senderName: 'Mãe',
      content: 'João, não te esqueças de arrumar o quarto! 😊',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 min ago
      status: MessageStatus.SENT,
    },
    unreadCount: 2,
    status: ChatStatus.ACTIVE,
    isPinned: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'chat-parent-1-adult',
    name: 'Mãe',
    type: ChatType.PRIVATE,
    participants: mockFamilyMembers.slice(0, 2),
    lastMessage: {
      id: 'msg-2',
      senderId: mockChildUser.id,
      senderName: mockChildUser.name,
      content: 'Já arrumei o quarto! 🎉',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 min ago
      status: MessageStatus.SENT,
    },
    unreadCount: 0,
    status: ChatStatus.ACTIVE,
    isPinned: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'chat-parent-2-adult',
    name: 'Pai',
    type: ChatType.PRIVATE,
    participants: mockFamilyMembers.slice(1, 3),
    lastMessage: {
      id: 'msg-3',
      senderId: 'parent-2',
      senderName: 'Pai',
      content: 'Bom trabalho no teste de matemática! ⭐',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      status: MessageStatus.SENT,
    },
    unreadCount: 0,
    status: ChatStatus.ACTIVE,
    isPinned: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  }
];

/**
 * ChildChatPageAdultStyle component - Chat interface for children using adult visual styling
 * Uses the same visual components as adult chat but with child-specific functionality and navigation
 */
const ChildChatPageAdultStyle: React.FC = () => {
  const navigate = useNavigate();
  const [activeFilter, setActiveFilter] = useState<ChatFilterType>(ChatFilterType.ALL);
  const [chats, setChats] = useState<Chat[]>(mockChildChatsAdultStyle);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [activeCall, setActiveCall] = useState<CallRecord | null>(null);
  const [isCallActive, setIsCallActive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [activeSection, setActiveSection] = useState<ChildDashboardSection>(ChildDashboardSection.FAMILY_CHAT);

  const currentUser = mockChildUser;

  // Filter chats based on active filter
  const getFilteredChats = useCallback((): Chat[] => {
    let filteredChats = [...chats];

    switch (activeFilter) {
      case ChatFilterType.UNREAD:
        filteredChats = filteredChats.filter(chat => chat.unreadCount > 0);
        break;
      case ChatFilterType.RECEIVED:
        filteredChats = filteredChats.filter(chat => 
          chat.lastMessage && chat.lastMessage.senderId !== currentUser.id
        );
        break;
      case ChatFilterType.ARCHIVED:
        filteredChats = filteredChats.filter(chat => chat.status === 'archived');
        break;
      case ChatFilterType.ALL:
      default:
        filteredChats = filteredChats.filter(chat => chat.status !== 'archived');
        break;
    }

    // Sort by pinned status and last message time
    return filteredChats.sort((a, b) => {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      
      const aTime = a.lastMessage?.timestamp.getTime() || 0;
      const bTime = b.lastMessage?.timestamp.getTime() || 0;
      return bTime - aTime;
    });
  }, [chats, activeFilter, currentUser.id]);

  const handleChatSelect = (chatId: string) => {
    setSelectedChat(chatId);
    
    // Mark messages as read
    setChats(prevChats => 
      prevChats.map(chat => 
        chat.id === chatId 
          ? { ...chat, unreadCount: 0 }
          : chat
      )
    );
    console.log('Selected chat:', chatId);
  };

  const handleBackToList = () => {
    setSelectedChat(null);
  };

  const handleSwipeAction = (chatId: string, action: string) => {
    setChats(prevChats => 
      prevChats.map(chat => {
        if (chat.id !== chatId) return chat;

        switch (action) {
          case 'archive':
            return { ...chat, status: 'archived' as const };
          case 'unarchive':
            return { ...chat, status: 'active' as const };
          case 'mark-read':
            return { ...chat, unreadCount: 0 };
          case 'mark-unread':
            return { ...chat, unreadCount: Math.max(1, chat.unreadCount) };
          case 'mute':
            return { ...chat, status: 'muted' as const };
          case 'unmute':
            return { ...chat, status: 'active' as const };
          case 'delete':
            // Only allow deleting private chats
            if (chat.type === ChatType.PRIVATE) {
              return null;
            }
            return chat;
          default:
            return chat;
        }
      }).filter(Boolean) as Chat[]
    );

    console.log(`Action ${action} performed on chat ${chatId}`);
  };

  const handleCreatePrivateChat = (participantId: string) => {
    const participant = mockFamilyMembers.find(m => m.id === participantId);
    if (!participant) return;

    const newChat: Chat = {
      id: `private-chat-${Date.now()}`,
      type: ChatType.PRIVATE,
      name: participant.name,
      participants: [currentUser, participant],
      unreadCount: 0,
      status: 'active',
      isPinned: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setChats(prevChats => [...prevChats, newChat]);
    console.log('Created private chat with:', participant.name);
  };

  const handleStartCall = (type: CallType) => {
    const currentChat = chats.find(chat => chat.id === selectedChat);
    const participants = currentChat ? currentChat.participants : mockFamilyMembers;

    const newCall: CallRecord = {
      id: `call-${Date.now()}`,
      type,
      status: CallStatus.OUTGOING,
      participants,
      duration: 0,
      timestamp: new Date(),
      initiatorId: currentUser.id
    };

    setActiveCall(newCall);
    setIsCallActive(true);

    // Simulate call connecting
    setTimeout(() => {
      setActiveCall(prev => prev ? { ...prev, status: CallStatus.ACTIVE } : null);
    }, 3000);

    console.log('Started call:', type);
  };

  const handleEndCall = () => {
    setActiveCall(null);
    setIsCallActive(false);
    console.log('Call ended');
  };

  const handleToggleVideo = () => {
    console.log('Toggle video');
  };

  const handleToggleMute = () => {
    console.log('Toggle mute');
  };

  const handleShowCallHistory = () => {
    console.log('Show call history');
    // In a real app, this would open a call history modal
  };

  const handleSectionChange = (section: ChildDashboardSection) => {
    setActiveSection(section);

    switch (section) {
      case ChildDashboardSection.HOME:
        navigate('/child-dashboard');
        break;
      case ChildDashboardSection.MY_TASKS:
        navigate('/child-tasks');
        break;
      case ChildDashboardSection.MY_REWARDS:
        navigate('/child-rewards');
        break;
      case ChildDashboardSection.FAMILY_CHAT:
        // Stay on chat page
        break;
      default:
        break;
    }
  };

  // Infinite scroll handler
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (scrollHeight - scrollTop === clientHeight && hasMore && !loading) {
      setLoading(true);

      // Simulate loading more chats
      setTimeout(() => {
        setLoading(false);
        // In a real app, you would load more chats here
        console.log('Load more chats...');
      }, 1000);
    }
  }, [hasMore, loading]);

  const filteredChats = getFilteredChats();
  const unreadCount = chats.filter(chat => chat.unreadCount > 0).length;
  const archivedCount = chats.filter(chat => chat.status === 'archived').length;
  const currentChat = chats.find(chat => chat.id === selectedChat);

  // If a chat is selected, show conversation view with iMessage style
  if (selectedChat && currentChat) {
    return (
      <div className="child-imessage-page">
        <ChildiMessageConversation
          chat={currentChat}
          currentUser={currentUser}
          onBack={handleBackToList}
          onStartCall={handleStartCall}
        />

        {/* Child Mobile Bottom Navbar */}
        <ChildMobileNavbar
          activeSection={activeSection}
          onSectionChange={handleSectionChange}
          unreadCount={unreadCount}
        />

        {/* Call Interface */}
        <CallInterface
          call={activeCall}
          isActive={isCallActive}
          onEndCall={handleEndCall}
          onToggleVideo={handleToggleVideo}
          onToggleMute={handleToggleMute}
        />
      </div>
    );
  }

  // Default chat list view with adult styling
  return (
    <div className="chat-page">
      {/* Header - Custom header for child with adult styling */}
      <div className="chat-header">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-auto">
              <button
                className="btn btn-link text-white p-0 me-3"
                onClick={() => navigate('/child-dashboard')}
                title="Voltar ao Dashboard"
              >
                <i className="fas fa-arrow-left fa-lg"></i>
              </button>
            </div>

            <div className="col-auto">
              <div className="text-white fw-bold" style={{ fontSize: '1.2rem' }}>
                SuperTarefa
              </div>
            </div>

            <div className="col">
              <h4 className="mb-0 text-white fw-bold">Chat da Família</h4>
              <small className="text-white-50">Chat da família</small>
            </div>

            <div className="col-auto">
              <div className="btn-group">
                <button
                  className="btn btn-outline-light btn-sm"
                  onClick={() => handleStartCall(CallType.AUDIO)}
                  title="Chamada de áudio"
                >
                  <i className="fas fa-phone"></i>
                </button>

                <button
                  className="btn btn-outline-light btn-sm"
                  onClick={() => handleStartCall(CallType.VIDEO)}
                  title="Chamada de vídeo"
                >
                  <i className="fas fa-video"></i>
                </button>

                <button
                  className="btn btn-outline-light btn-sm"
                  onClick={handleShowCallHistory}
                  title="Histórico de chamadas"
                >
                  <i className="fas fa-history"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filter Pills - Using adult ChatFilterPills component */}
      <ChatFilterPills
        activeFilter={activeFilter}
        onFilterChange={setActiveFilter}
        unreadCount={unreadCount}
        archivedCount={archivedCount}
      />

      {/* Chat List - Using adult ChatItem components */}
      <div className="chat-main">
        <div
          className="chat-list list-group list-group-flush"
          onScroll={handleScroll}
        >
          {filteredChats.length === 0 ? (
            <div className="text-center py-5">
              <i className="fas fa-comments fa-3x text-muted mb-3"></i>
              <h5 className="text-muted">Nenhuma conversa encontrada</h5>
              <p className="text-muted">
                {activeFilter === ChatFilterType.UNREAD && 'Não tens mensagens não lidas'}
                {activeFilter === ChatFilterType.ARCHIVED && 'Não tens conversas arquivadas'}
                {activeFilter === ChatFilterType.RECEIVED && 'Não tens mensagens recebidas'}
                {activeFilter === ChatFilterType.ALL && 'Começa uma conversa!'}
              </p>
            </div>
          ) : (
            <>
              {filteredChats.map(chat => (
                <ChatItem
                  key={chat.id}
                  chat={chat}
                  currentUser={currentUser}
                  onChatSelect={handleChatSelect}
                  onSwipeAction={handleSwipeAction}
                />
              ))}

              {loading && (
                <div className="text-center py-3">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">A carregar...</span>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* Floating Action Button */}
        <div className="position-fixed" style={{ bottom: '100px', right: '20px', zIndex: 1000 }}>
          <button
            className="btn btn-primary btn-lg rounded-circle"
            onClick={() => setShowCreateModal(true)}
            title="Nova conversa"
            style={{ width: '60px', height: '60px' }}
          >
            <i className="fas fa-plus"></i>
          </button>
        </div>
      </div>

      {/* Child Mobile Bottom Navbar */}
      <ChildMobileNavbar
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
        unreadCount={unreadCount}
      />

      {/* Create Private Chat Modal */}
      <CreatePrivateChatModal
        show={showCreateModal}
        onHide={() => setShowCreateModal(false)}
        familyMembers={mockFamilyMembers}
        existingChats={chats}
        currentUser={currentUser}
        onCreateChat={handleCreatePrivateChat}
      />

      {/* Call Interface */}
      <CallInterface
        call={activeCall}
        isActive={isCallActive}
        onEndCall={handleEndCall}
        onToggleVideo={handleToggleVideo}
        onToggleMute={handleToggleMute}
      />
    </div>
  );
};

export default ChildChatPageAdultStyle;
