import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Logo from '../components/Logo';
import GoogleLoginButton from '../components/GoogleLoginButton';

/**
 * AdultRegisterPage component - Registration page for adults
 * Features comprehensive registration form with Google signup integration
 */
const AdultRegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const { loginWithGoogle, userProfile } = useAuth();
  
  // Track Google register success for redirection
  const [googleRegisterSuccess, setGoogleRegisterSuccess] = useState(false);

  // Handle redirection after Google register when user profile is loaded
  useEffect(() => {
    if (googleRegisterSuccess && userProfile) {
      // After Google registration, always go to family setup
      navigate('/family-setup', { replace: true });
      setGoogleRegisterSuccess(false);
    }
  }, [googleRegisterSuccess, userProfile, navigate]);

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError(''); // Clear error when user starts typing
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Basic validation
      if (!formData.firstName || !formData.lastName || !formData.email || !formData.password || !formData.confirmPassword) {
        setError('Por favor, preenche todos os campos.');
        return;
      }

      if (!formData.email.includes('@')) {
        setError('Por favor, insere um email válido.');
        return;
      }

      if (formData.password.length < 6) {
        setError('A palavra-passe deve ter pelo menos 6 caracteres.');
        return;
      }

      if (formData.password !== formData.confirmPassword) {
        setError('As palavras-passe não coincidem.');
        return;
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      console.log('Adult register with:', formData);
      // After registration, go to family setup
      navigate('/family-setup');
    } catch (err) {
      setError('Erro ao criar conta. Tenta novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleRegister = async () => {
    setIsLoading(true);
    setError('');

    try {
      await loginWithGoogle();
      
      // Set flag to trigger redirection when user profile is loaded
      setGoogleRegisterSuccess(true);
    } catch (err) {
      console.error('Google register error:', err);
      setError('Erro ao registar com Google. Tenta novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    navigate('/');
  };

  const handleGoToLogin = () => {
    navigate('/adult-login');
  };

  return (
    <div className="adult-login-page min-vh-100 d-flex align-items-center">
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-md-6 col-lg-5">
            <div className="card shadow-lg border-0 adult-login-card">
              <div className="card-body p-5">
                {/* Logo */}
                <div className="text-center mb-4">
                  <Logo size="sm" className="mb-3" />
                  <h2 className="card-title fw-bold mb-2 adult-login-title">
                    <i className="fas fa-user-plus me-2 adult-icon"></i>
                    Criar Conta
                  </h2>
                  <p className="text-muted">
                    Junta-te à SuperTarefa e organiza a tua família
                  </p>
                </div>

                {/* Error Message */}
                {error && (
                  <div className="alert alert-danger alert-dismissible fade show" role="alert">
                    <i className="fas fa-exclamation-triangle me-2"></i>
                    {error}
                  </div>
                )}

                {/* Registration Form */}
                <form onSubmit={handleSubmit}>
                  {/* Name Fields */}
                  <div className="row mb-3">
                    <div className="col-md-6">
                      <label htmlFor="firstName" className="form-label fw-semibold">
                        <i className="fas fa-user me-2"></i>
                        Nome
                      </label>
                      <input
                        type="text"
                        className="form-control"
                        id="firstName"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        placeholder="O teu nome"
                        disabled={isLoading}
                        required
                      />
                    </div>
                    <div className="col-md-6">
                      <label htmlFor="lastName" className="form-label fw-semibold">
                        Apelido
                      </label>
                      <input
                        type="text"
                        className="form-control"
                        id="lastName"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        placeholder="O teu apelido"
                        disabled={isLoading}
                        required
                      />
                    </div>
                  </div>

                  {/* Email Field */}
                  <div className="mb-3">
                    <label htmlFor="email" className="form-label fw-semibold">
                      <i className="fas fa-envelope me-2"></i>
                      Email
                    </label>
                    <input
                      type="email"
                      className="form-control"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      disabled={isLoading}
                      required
                    />
                  </div>

                  {/* Password Field */}
                  <div className="mb-3">
                    <label htmlFor="password" className="form-label fw-semibold">
                      <i className="fas fa-lock me-2"></i>
                      Palavra-passe
                    </label>
                    <div className="input-group">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        className="form-control"
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        placeholder="Mínimo 6 caracteres"
                        disabled={isLoading}
                        required
                      />
                      <button
                        type="button"
                        className="btn btn-outline-secondary"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={isLoading}
                      >
                        <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                      </button>
                    </div>
                  </div>

                  {/* Confirm Password Field */}
                  <div className="mb-4">
                    <label htmlFor="confirmPassword" className="form-label fw-semibold">
                      <i className="fas fa-lock me-2"></i>
                      Confirmar Palavra-passe
                    </label>
                    <div className="input-group">
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        className="form-control"
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        placeholder="Repete a palavra-passe"
                        disabled={isLoading}
                        required
                      />
                      <button
                        type="button"
                        className="btn btn-outline-secondary"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        disabled={isLoading}
                      >
                        <i className={`fas ${showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                      </button>
                    </div>
                  </div>

                  {/* Register Button */}
                  <button
                    type="submit"
                    className="btn btn-primary btn-lg w-100 mb-3 adult-register-btn"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                        A criar conta...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-user-plus me-2"></i>
                        Criar Conta
                      </>
                    )}
                  </button>
                </form>

                {/* Divider */}
                <div className="text-center mb-3">
                  <span className="text-muted">ou</span>
                </div>

                {/* Google Register */}
                <div className="mb-4">
                  <GoogleLoginButton
                    onClick={handleGoogleRegister}
                    disabled={isLoading}
                  />
                </div>

                {/* Login Link */}
                <div className="text-center mb-3">
                  <span className="text-muted">Já tens conta? </span>
                  <button
                    type="button"
                    className="btn btn-link p-0 text-decoration-none"
                    onClick={handleGoToLogin}
                    disabled={isLoading}
                  >
                    Faz login aqui
                  </button>
                </div>

                {/* Back Button */}
                <div className="text-center">
                  <button
                    type="button"
                    className="btn btn-outline-secondary"
                    onClick={handleBackToLogin}
                    disabled={isLoading}
                  >
                    <i className="fas fa-arrow-left me-2"></i>
                    Voltar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdultRegisterPage;