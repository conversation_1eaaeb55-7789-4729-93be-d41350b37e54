import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { FirebaseError } from 'firebase/app';

/**
 * RegisterPage component - Adult registration page with Firebase authentication
 */
const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const { register } = useAuth();

  // Form state
  const [formData, setFormData] = useState({
    displayName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  // Validate form
  const validateForm = () => {
    if (!formData.displayName.trim()) {
      setError('Por favor, insira o seu nome.');
      return false;
    }

    if (!formData.email) {
      setError('Por favor, insira o seu email.');
      return false;
    }

    if (!formData.email.includes('@')) {
      setError('Por favor, insira um email válido.');
      return false;
    }

    if (formData.password.length < 6) {
      setError('A palavra-passe deve ter pelo menos 6 caracteres.');
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('As palavras-passe não coincidem.');
      return false;
    }

    return true;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      await register(formData.email, formData.password, formData.displayName);
      navigate('/adult-dashboard', { replace: true });
    } catch (error) {
      console.error('Registration error:', error);
      
      if (error instanceof FirebaseError) {
        switch (error.code) {
          case 'auth/email-already-in-use':
            setError('Este email já está em uso. Tente fazer login.');
            break;
          case 'auth/invalid-email':
            setError('Email inválido.');
            break;
          case 'auth/weak-password':
            setError('A palavra-passe é muito fraca. Use pelo menos 6 caracteres.');
            break;
          case 'auth/operation-not-allowed':
            setError('Registo não permitido. Contacte o suporte.');
            break;
          default:
            setError('Erro ao criar conta. Tente novamente.');
        }
      } else {
        setError('Erro ao criar conta. Tente novamente.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="register-page">
      <div className="container-fluid h-100">
        <div className="row h-100">
          {/* Left side - Branding */}
          <div className="col-lg-6 d-none d-lg-flex login-branding">
            <div className="branding-content">
              <div className="brand-logo">
                <h1 className="brand-title">SuperTarefa</h1>
                <p className="brand-subtitle">Junte-se à nossa família!</p>
              </div>
              <div className="brand-features">
                <div className="feature-item">
                  <i className="fas fa-users feature-icon"></i>
                  <div>
                    <h4>Gestão Familiar</h4>
                    <p>Organize toda a família numa só plataforma</p>
                  </div>
                </div>
                <div className="feature-item">
                  <i className="fas fa-shield-alt feature-icon"></i>
                  <div>
                    <h4>Seguro e Privado</h4>
                    <p>Os seus dados estão protegidos e seguros</p>
                  </div>
                </div>
                <div className="feature-item">
                  <i className="fas fa-mobile-alt feature-icon"></i>
                  <div>
                    <h4>Acesso Móvel</h4>
                    <p>Use em qualquer dispositivo, em qualquer lugar</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Register form */}
          <div className="col-lg-6 d-flex align-items-center justify-content-center login-form-section">
            <div className="login-form-container">
              <div className="text-center mb-4">
                <h2 className="login-title">Criar Conta</h2>
                <p className="login-subtitle">Registe-se como adulto responsável</p>
              </div>

              {error && (
                <div className="alert alert-danger" role="alert">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </div>
              )}

              <form onSubmit={handleSubmit} className="login-form">
                <div className="mb-3">
                  <label htmlFor="displayName" className="form-label">
                    <i className="fas fa-user me-2"></i>
                    Nome Completo
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    id="displayName"
                    name="displayName"
                    value={formData.displayName}
                    onChange={handleChange}
                    placeholder="O seu nome completo"
                    required
                    disabled={loading}
                  />
                </div>

                <div className="mb-3">
                  <label htmlFor="email" className="form-label">
                    <i className="fas fa-envelope me-2"></i>
                    Email
                  </label>
                  <input
                    type="email"
                    className="form-control"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="<EMAIL>"
                    required
                    disabled={loading}
                  />
                </div>

                <div className="mb-3">
                  <label htmlFor="password" className="form-label">
                    <i className="fas fa-lock me-2"></i>
                    Palavra-passe
                  </label>
                  <input
                    type="password"
                    className="form-control"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    placeholder="Mínimo 6 caracteres"
                    required
                    disabled={loading}
                  />
                </div>

                <div className="mb-3">
                  <label htmlFor="confirmPassword" className="form-label">
                    <i className="fas fa-lock me-2"></i>
                    Confirmar Palavra-passe
                  </label>
                  <input
                    type="password"
                    className="form-control"
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    placeholder="Repita a palavra-passe"
                    required
                    disabled={loading}
                  />
                </div>

                <div className="mb-3">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="acceptTerms"
                      required
                    />
                    <label className="form-check-label" htmlFor="acceptTerms">
                      Aceito os <Link to="/terms">Termos de Serviço</Link> e a <Link to="/privacy">Política de Privacidade</Link>
                    </label>
                  </div>
                </div>

                <button
                  type="submit"
                  className="btn btn-primary w-100 login-btn"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                      A criar conta...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-user-plus me-2"></i>
                      Criar Conta
                    </>
                  )}
                </button>
              </form>

              <div className="text-center mt-4">
                <p className="register-link">
                  Já tem conta? 
                  <Link to="/adult-login" className="ms-2">
                    Faça login aqui
                  </Link>
                </p>
              </div>

              <div className="text-center mt-3">
                <Link to="/" className="back-link">
                  <i className="fas fa-arrow-left me-2"></i>
                  Voltar ao início
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
