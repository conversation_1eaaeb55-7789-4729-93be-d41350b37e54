import React from 'react';
import { Link } from 'react-router-dom';

/**
 * HomePage component - Main landing page for the SuperTarefa application
 * Displays the primary interface for task management and family coordination
 */
const HomePage: React.FC = () => {
  return (
    <div className="container-fluid">
      <div className="row justify-content-center">
        <div className="col-md-8 col-lg-6">
          <div className="text-center py-5">
            <h1 className="display-4 mb-4">SuperTarefa</h1>
            <p className="lead mb-4">
              A plataforma perfeita para organizar tarefas familiares e promover a colaboração entre pais e filhos.
            </p>
            <div className="d-grid gap-2 d-md-flex justify-content-md-center">
              <Link to="/register" className="btn btn-primary btn-lg me-md-2">
                <PERSON><PERSON><PERSON>
              </Link>
              <Link to="/adult-login" className="btn btn-outline-primary btn-lg me-md-2">
                Fazer <PERSON>gin
              </Link>
              <Link to="/child-dashboard" className="btn btn-outline-secondary btn-lg">
                Acesso Criança
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
