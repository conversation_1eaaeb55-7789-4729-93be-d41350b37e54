import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { ShopItem } from '../types/schema';
import { ShopItemStatus, ShopItemCategory, ChildDashboardSection } from '../types/enums';
import { mockShopItems } from '../data/ShopMockData';
import ChildMobileNavbar from '../components/ChildMobileNavbar';

/**
 * ChildThemesPage component - Child-friendly themes page
 * Allows children to view and select available themes
 */
const ChildThemesPage: React.FC = () => {
  const navigate = useNavigate();
  
  // Mock current child user
  const currentChild = {
    id: 'child-123',
    name: '<PERSON>',
    points: 1250,
    avatar: '👦',
    currentTheme: 'spiderman-theme'
  };

  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState<ShopItem | null>(null);

  // Filter themes available for children
  const availableThemes = useMemo(() => {
    return mockShopItems.filter(item => 
      item.type === 'theme' && 
      (selectedCategory === 'all' || item.category === selectedCategory)
    );
  }, [selectedCategory]);

  // Theme categories for children
  const themeCategories = [
    { id: 'all', name: 'Todos', icon: '🎨', color: '#FF6B35' },
    { id: ShopItemCategory.SUPERHERO, name: 'Super-Heróis', icon: '🦸‍♂️', color: '#DC2626' },
    { id: ShopItemCategory.PRINCESS, name: 'Princesas', icon: '👸', color: '#EC4899' },
    { id: ShopItemCategory.ANIMAL, name: 'Animais', icon: '🐾', color: '#059669' },
    { id: ShopItemCategory.SPACE, name: 'Espaço', icon: '🚀', color: '#7C3AED' },
    { id: ShopItemCategory.FANTASY, name: 'Fantasia', icon: '🧙‍♂️', color: '#7C2D12' },
    { id: ShopItemCategory.SPORTS, name: 'Desportos', icon: '⚽', color: '#EA580C' }
  ];

  // Handle theme selection
  const handleThemeSelect = (theme: ShopItem) => {
    if (theme.pricePoints <= currentChild.points || theme.status === ShopItemStatus.PURCHASED) {
      setSelectedTheme(theme);
      setShowPreviewModal(true);
    }
  };

  // Handle theme activation
  const handleActivateTheme = () => {
    if (selectedTheme) {
      // In a real app, this would update the user's current theme
      console.log('Activating theme:', selectedTheme.id);
      setShowPreviewModal(false);
      setSelectedTheme(null);
      
      // Show success message
      alert(`🎉 Tema "${selectedTheme.name}" ativado com sucesso!`);
    }
  };

  // Get theme status
  const getThemeStatus = (theme: ShopItem) => {
    if (theme.id === currentChild.currentTheme) {
      return 'active';
    } else if (theme.status === ShopItemStatus.PURCHASED) {
      return 'purchased';
    } else if (theme.pricePoints <= currentChild.points) {
      return 'available';
    } else {
      return 'locked';
    }
  };

  // Get rarity color
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return '#96CEB4';
      case 'rare': return '#4ECDC4';
      case 'epic': return '#FFD93D';
      case 'legendary': return '#FF6B6B';
      default: return '#96CEB4';
    }
  };

  // Get rarity name
  const getRarityName = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'Comum';
      case 'rare': return 'Raro';
      case 'epic': return 'Épico';
      case 'legendary': return 'Lendário';
      default: return 'Comum';
    }
  };

  return (
    <div className="child-themes-page">
      {/* Header */}
      <div className="child-themes-header">
        <div className="container-fluid px-3 py-4">
          <div className="d-flex align-items-center justify-content-between">
            <div className="d-flex align-items-center">
              <button 
                className="child-back-btn me-3"
                onClick={() => navigate('/child-dashboard')}
              >
                <i className="fas fa-arrow-left"></i>
              </button>
              <div>
                <h2 className="child-page-title mb-1">
                  🎨 Os Meus Temas
                </h2>
                <p className="child-page-subtitle mb-0">
                  Personaliza a tua aplicação!
                </p>
              </div>
            </div>
            <div className="child-points-display">
              <div className="points-badge">
                <i className="fas fa-star"></i>
                <span>{currentChild.points}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Categories Filter */}
      <div className="child-categories-section">
        <div className="container-fluid px-3">
          <div className="categories-scroll">
            {themeCategories.map(category => (
              <button
                key={category.id}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category.id)}
                style={{ '--category-color': category.color } as React.CSSProperties}
              >
                <span className="category-icon">{category.icon}</span>
                <span className="category-name">{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Themes Grid */}
      <div className="child-themes-content">
        <div className="container-fluid px-3">
          {availableThemes.length === 0 ? (
            <div className="empty-state">
              <div className="empty-icon">🎨</div>
              <h4>Nenhum tema disponível</h4>
              <p>Mais temas em breve!</p>
            </div>
          ) : (
            <div className="themes-grid">
              {availableThemes.map(theme => {
                const status = getThemeStatus(theme);
                
                return (
                  <div 
                    key={theme.id} 
                    className={`theme-card ${status}`}
                    onClick={() => handleThemeSelect(theme)}
                  >
                    <div className="theme-image-container">
                      <img 
                        src={theme.imageUrl} 
                        alt={theme.name}
                        className="theme-image"
                      />
                      
                      {/* Status overlay */}
                      {status === 'active' && (
                        <div className="theme-status-overlay active">
                          <i className="fas fa-check-circle"></i>
                          <span>Ativo</span>
                        </div>
                      )}
                      
                      {status === 'locked' && (
                        <div className="theme-status-overlay locked">
                          <i className="fas fa-lock"></i>
                        </div>
                      )}
                      
                      {/* Rarity badge */}
                      <div 
                        className="theme-rarity-badge"
                        style={{ backgroundColor: getRarityColor(theme.rarity) }}
                      >
                        {getRarityName(theme.rarity)}
                      </div>
                    </div>
                    
                    <div className="theme-content">
                      <h4 className="theme-title">{theme.name}</h4>
                      <p className="theme-description">{theme.description}</p>
                      
                      <div className="theme-footer">
                        {status === 'active' ? (
                          <div className="theme-active-badge">
                            <i className="fas fa-check me-2"></i>
                            Tema Ativo
                          </div>
                        ) : status === 'purchased' ? (
                          <button className="theme-activate-btn">
                            <i className="fas fa-palette me-2"></i>
                            Ativar Tema
                          </button>
                        ) : status === 'available' ? (
                          <div className="theme-price">
                            <i className="fas fa-star me-2"></i>
                            {theme.pricePoints} pontos
                          </div>
                        ) : (
                          <div className="theme-locked">
                            <i className="fas fa-lock me-2"></i>
                            Precisas de {theme.pricePoints - currentChild.points} pontos
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Preview Modal */}
      {showPreviewModal && selectedTheme && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered modal-lg">
            <div className="modal-content child-modal">
              <div className="modal-header" style={{ backgroundColor: getRarityColor(selectedTheme.rarity) }}>
                <h5 className="modal-title text-white">
                  🎨 {selectedTheme.name}
                </h5>
                <button
                  className="btn-close btn-close-white"
                  onClick={() => setShowPreviewModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <div className="row">
                  <div className="col-md-6">
                    <img 
                      src={selectedTheme.imageUrl} 
                      alt={selectedTheme.name}
                      className="img-fluid rounded"
                    />
                  </div>
                  <div className="col-md-6">
                    <h4>{selectedTheme.name}</h4>
                    <p>{selectedTheme.description}</p>
                    
                    <div className="theme-details">
                      <div className="detail-item">
                        <strong>Raridade:</strong> 
                        <span 
                          className="rarity-badge ms-2"
                          style={{ backgroundColor: getRarityColor(selectedTheme.rarity) }}
                        >
                          {getRarityName(selectedTheme.rarity)}
                        </span>
                      </div>
                      
                      <div className="detail-item">
                        <strong>Preço:</strong> 
                        <span className="price-badge ms-2">
                          <i className="fas fa-star"></i>
                          {selectedTheme.pricePoints} pontos
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  className="btn btn-secondary"
                  onClick={() => setShowPreviewModal(false)}
                >
                  Cancelar
                </button>
                <button
                  className="btn btn-primary"
                  onClick={handleActivateTheme}
                >
                  <i className="fas fa-palette me-2"></i>
                  Ativar Tema
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Navigation */}
      <ChildMobileNavbar
        activeSection={ChildDashboardSection.MY_THEMES}
        onSectionChange={(section) => {
          if (section === ChildDashboardSection.DASHBOARD) navigate('/child-dashboard');
          if (section === ChildDashboardSection.TASKS) navigate('/child-tasks');
          if (section === ChildDashboardSection.CHAT) navigate('/child-chat-adult-style');
          if (section === ChildDashboardSection.REWARDS) navigate('/child-rewards');
          if (section === ChildDashboardSection.TROPHIES) navigate('/child-trophies');
          if (section === ChildDashboardSection.CALENDAR) navigate('/child-calendar');
        }}
        unreadCount={0}
      />
    </div>
  );
};

export default ChildThemesPage;
