import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Trophy } from '../types/schema';
import { TrophyRarity, ChildDashboardSection } from '../types/enums';
import { mockTrophies, mockTrophyCategories } from '../data/TrophyMockData';
import ChildMobileNavbar from '../components/ChildMobileNavbar';

/**
 * ChildTrophiesPage component - Child-friendly trophies and achievements page
 * Shows trophies in a fun, engaging way with progress tracking
 */
const ChildTrophiesPage: React.FC = () => {
  const navigate = useNavigate();
  
  // Mock current child user
  const currentChild = {
    id: 'child-123',
    name: '<PERSON>',
    avatar: '👦'
  };

  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'achieved' | 'pending'>('all');

  // Filter trophies for current child
  const childTrophies = useMemo(() => {
    let filtered = mockTrophies.filter(trophy => 
      trophy.childId === currentChild.id || 
      trophy.childId === 'child-1' || 
      !trophy.childId // Global trophies
    );

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(trophy => trophy.category.id === selectedCategory);
    }

    // Apply achievement filter
    if (selectedFilter === 'achieved') {
      filtered = filtered.filter(trophy => trophy.isAchieved);
    } else if (selectedFilter === 'pending') {
      filtered = filtered.filter(trophy => !trophy.isAchieved);
    }

    return filtered;
  }, [currentChild.id, selectedCategory, selectedFilter]);

  // Calculate statistics
  const stats = useMemo(() => {
    const total = childTrophies.length;
    const achieved = childTrophies.filter(t => t.isAchieved).length;
    const pending = total - achieved;
    const completionRate = total > 0 ? Math.round((achieved / total) * 100) : 0;

    return { total, achieved, pending, completionRate };
  }, [childTrophies]);

  const getRarityColor = (rarity: TrophyRarity) => {
    switch (rarity) {
      case TrophyRarity.COMMON:
        return '#96CEB4';
      case TrophyRarity.RARE:
        return '#4ECDC4';
      case TrophyRarity.EPIC:
        return '#FFD93D';
      case TrophyRarity.LEGENDARY:
        return '#FF6B6B';
      default:
        return '#96CEB4';
    }
  };

  const getRarityName = (rarity: TrophyRarity) => {
    switch (rarity) {
      case TrophyRarity.COMMON:
        return 'Comum';
      case TrophyRarity.RARE:
        return 'Raro';
      case TrophyRarity.EPIC:
        return 'Épico';
      case TrophyRarity.LEGENDARY:
        return 'Lendário';
      default:
        return 'Comum';
    }
  };

  const handleTrophyClick = (trophy: Trophy) => {
    // Show trophy details modal or navigate to details
    console.log('Trophy clicked:', trophy);
  };

  return (
    <div className="child-trophies-page">
      {/* Header */}
      <div className="child-trophies-header">
        <div className="container-fluid px-3 py-4">
          <div className="d-flex align-items-center justify-content-between">
            <div className="d-flex align-items-center">
              <button 
                className="child-back-btn me-3"
                onClick={() => navigate('/child-dashboard')}
              >
                <i className="fas fa-arrow-left"></i>
              </button>
              <div>
                <h2 className="child-page-title mb-1">
                  🏆 Os Meus Troféus
                </h2>
                <p className="child-page-subtitle mb-0">
                  {stats.achieved} de {stats.total} troféus conquistados!
                </p>
              </div>
            </div>
            <div className="trophy-progress-circle">
              <div className="progress-circle" style={{ '--progress': `${stats.completionRate}%` } as React.CSSProperties}>
                <span className="progress-text">{stats.completionRate}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="child-trophies-stats">
        <div className="container-fluid px-3">
          <div className="stats-grid">
            <div className="stat-card achieved">
              <div className="stat-icon">🏆</div>
              <div className="stat-content">
                <div className="stat-number">{stats.achieved}</div>
                <div className="stat-label">Conquistados</div>
              </div>
            </div>
            <div className="stat-card pending">
              <div className="stat-icon">🎯</div>
              <div className="stat-content">
                <div className="stat-number">{stats.pending}</div>
                <div className="stat-label">Por conquistar</div>
              </div>
            </div>
            <div className="stat-card total">
              <div className="stat-icon">⭐</div>
              <div className="stat-content">
                <div className="stat-number">{stats.total}</div>
                <div className="stat-label">Total</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters Section */}
      <div className="child-trophies-filters">
        <div className="container-fluid px-3">
          {/* Achievement Filter */}
          <div className="filter-tabs">
            <button
              className={`filter-tab ${selectedFilter === 'all' ? 'active' : ''}`}
              onClick={() => setSelectedFilter('all')}
            >
              <span className="tab-icon">🎯</span>
              <span className="tab-text">Todos</span>
            </button>
            <button
              className={`filter-tab ${selectedFilter === 'achieved' ? 'active' : ''}`}
              onClick={() => setSelectedFilter('achieved')}
            >
              <span className="tab-icon">🏆</span>
              <span className="tab-text">Conquistados</span>
            </button>
            <button
              className={`filter-tab ${selectedFilter === 'pending' ? 'active' : ''}`}
              onClick={() => setSelectedFilter('pending')}
            >
              <span className="tab-icon">⏳</span>
              <span className="tab-text">Pendentes</span>
            </button>
          </div>

          {/* Category Filter */}
          <div className="categories-scroll">
            <button
              className={`category-btn ${selectedCategory === 'all' ? 'active' : ''}`}
              onClick={() => setSelectedCategory('all')}
            >
              <span className="category-icon">🎯</span>
              <span className="category-name">Todas</span>
            </button>
            {mockTrophyCategories.map(category => (
              <button
                key={category.id}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category.id)}
              >
                <span className="category-icon">{category.icon}</span>
                <span className="category-name">{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Trophies Grid */}
      <div className="child-trophies-content">
        <div className="container-fluid px-3">
          {childTrophies.length === 0 ? (
            <div className="empty-state">
              <div className="empty-icon">🏆</div>
              <h4>Nenhum troféu encontrado</h4>
              <p>Completa mais tarefas para desbloquear troféus!</p>
            </div>
          ) : (
            <div className="trophies-grid">
              {childTrophies.map(trophy => (
                <div 
                  key={trophy.id} 
                  className={`trophy-card ${trophy.isAchieved ? 'achieved' : 'locked'}`}
                  onClick={() => handleTrophyClick(trophy)}
                >
                  <div className="trophy-header">
                    <div 
                      className="trophy-rarity-badge" 
                      style={{ backgroundColor: getRarityColor(trophy.rarity) }}
                    >
                      {getRarityName(trophy.rarity)}
                    </div>
                    {trophy.isAchieved && (
                      <div className="achievement-badge">
                        <i className="fas fa-check"></i>
                      </div>
                    )}
                  </div>
                  
                  <div className="trophy-icon-container">
                    <div 
                      className={`trophy-icon ${trophy.isAchieved ? 'achieved' : 'locked'}`}
                      style={{ color: trophy.isAchieved ? trophy.color : '#ccc' }}
                    >
                      {trophy.icon}
                    </div>
                    {trophy.isAchieved && (
                      <div className="shine-effect"></div>
                    )}
                  </div>
                  
                  <div className="trophy-content">
                    <h5 className="trophy-title">{trophy.title}</h5>
                    <p className="trophy-description">{trophy.description}</p>
                    
                    {trophy.isAchieved ? (
                      <div className="trophy-achieved">
                        <div className="achieved-date">
                          <i className="fas fa-calendar"></i>
                          Conquistado em {trophy.achievedAt?.toLocaleDateString('pt-PT')}
                        </div>
                        <div className="trophy-points">
                          <i className="fas fa-star"></i>
                          +{trophy.pointsRequired} pontos
                        </div>
                      </div>
                    ) : (
                      <div className="trophy-progress">
                        {trophy.requirements && trophy.requirements.length > 0 && (
                          <div className="requirement-progress">
                            {trophy.requirements.map((req, index) => (
                              <div key={index} className="progress-bar-container">
                                <div className="progress-label">
                                  {req.description}
                                </div>
                                <div className="progress-bar">
                                  <div 
                                    className="progress-fill"
                                    style={{ 
                                      width: `${Math.min((req.currentValue / req.targetValue) * 100, 100)}%` 
                                    }}
                                  />
                                </div>
                                <div className="progress-text">
                                  {req.currentValue} / {req.targetValue}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                        <div className="trophy-points">
                          <i className="fas fa-star"></i>
                          {trophy.pointsRequired} pontos
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Mobile Navigation */}
      <ChildMobileNavbar
        activeSection={ChildDashboardSection.TROPHIES}
        onSectionChange={(section) => {
          if (section === ChildDashboardSection.DASHBOARD) navigate('/child-dashboard');
          if (section === ChildDashboardSection.TASKS) navigate('/child-tasks');
          if (section === ChildDashboardSection.CHAT) navigate('/child-chat-adult-style');
          if (section === ChildDashboardSection.REWARDS) navigate('/child-rewards');
          if (section === ChildDashboardSection.CALENDAR) navigate('/child-calendar');
        }}
        unreadCount={0}
      />
    </div>
  );
};

export default ChildTrophiesPage;
