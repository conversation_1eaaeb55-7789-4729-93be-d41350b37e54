import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { FirebaseError } from 'firebase/app';
import Logo from '../components/Logo';
import GoogleLoginButton from '../components/GoogleLoginButton';

/**
 * AdultLoginPage component - Adult login page with Firebase authentication
 */
const AdultLoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, loginWithGoogle, userProfile, currentUser } = useAuth();

  // Track Google login success for redirection
  const [googleLoginSuccess, setGoogleLoginSuccess] = useState(false);

  // Handle redirection after Google login when user profile is loaded
  useEffect(() => {
    if (googleLoginSuccess && userProfile) {
      if (userProfile.familyId) {
        // User has family, go to dashboard
        navigate('/adult-dashboard', { replace: true });
      } else {
        // New user, go to family setup
        navigate('/family-setup', { replace: true });
      }
      setGoogleLoginSuccess(false);
    }
  }, [googleLoginSuccess, userProfile, navigate]);

  // Form state
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  // Get redirect path from location state
  const from = location.state?.from?.pathname || '/adult-dashboard';

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.email || !formData.password) {
      setError('Por favor, preencha todos os campos.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await login(formData.email, formData.password);
      navigate(from, { replace: true });
    } catch (error) {
      console.error('Login error:', error);

      if (error instanceof FirebaseError) {
        switch (error.code) {
          case 'auth/user-not-found':
            setError('Utilizador não encontrado. Verifique o email.');
            break;
          case 'auth/wrong-password':
            setError('Palavra-passe incorreta.');
            break;
          case 'auth/invalid-email':
            setError('Email inválido.');
            break;
          case 'auth/user-disabled':
            setError('Esta conta foi desativada.');
            break;
          case 'auth/too-many-requests':
            setError('Muitas tentativas falhadas. Tente novamente mais tarde.');
            break;
          default:
            setError('Erro ao fazer login. Tente novamente.');
        }
      } else {
        setError('Erro ao fazer login. Tente novamente.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsLoading(true);
    setError('');

    try {
      await loginWithGoogle();
      
      // Set flag to trigger redirection when user profile is loaded
      setGoogleLoginSuccess(true);
    } catch (err) {
      console.error('Google login error:', err);
      setError('Erro ao fazer login com Google. Tenta novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    navigate('/');
  };

  return (
    <div className="adult-login-page min-vh-100 d-flex align-items-center">
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-md-6 col-lg-5">
            <div className="card shadow-lg border-0 adult-login-card">
              <div className="card-body p-5">
                {/* Logo */}
                <div className="text-center mb-4">
                  <Logo size="md" className="mb-3" />
                  <h2 className="card-title fw-bold mb-2 adult-login-title">
                    <i className="fas fa-user-tie me-2 adult-icon"></i>
                    Área dos Pais
                  </h2>
                  <p className="text-muted">
                    Gere tarefas e acompanhe o progresso da família
                  </p>
                </div>

                {/* Error Message */}
                {error && (
                  <div className="alert alert-danger alert-dismissible fade show" role="alert">
                    <i className="fas fa-exclamation-triangle me-2"></i>
                    {error}
                  </div>
                )}

                {/* Login Form */}
                <form onSubmit={handleSubmit}>
                  {/* Email Field */}
                  <div className="mb-3">
                    <label htmlFor="email" className="form-label fw-semibold">
                      <i className="fas fa-envelope me-2"></i>
                      Email
                    </label>
                    <input
                      type="email"
                      className="form-control form-control-lg"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                      disabled={loading}
                      required
                    />
                  </div>

                  {/* Password Field */}
                  <div className="mb-4">
                    <label htmlFor="password" className="form-label fw-semibold">
                      <i className="fas fa-lock me-2"></i>
                      Palavra-passe
                    </label>
                    <div className="input-group">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        className="form-control form-control-lg"
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        placeholder="A tua palavra-passe"
                        disabled={loading}
                        required
                      />
                      <button
                        type="button"
                        className="btn btn-outline-secondary"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={loading}
                      >
                        <i className="fas fa-eye"></i>
                      </button>
                    </div>
                  </div>

                  {/* Login Button */}
                  <button
                    type="submit"
                    className="btn btn-primary btn-lg w-100 mb-3 adult-login-btn"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                        A entrar...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-sign-in-alt me-2"></i>
                        Entrar
                      </>
                    )}
                  </button>
                </form>

                {/* Divider */}
                <div className="text-center mb-3">
                  <span className="text-muted">ou</span>
                </div>

                {/* Google Login */}
                <GoogleLoginButton
                  onClick={handleGoogleLogin}
                  disabled={isLoading}
                />

                {/* Forgot Password */}
                <div className="text-center mt-3 mb-4">
                  <a href="#" className="text-decoration-none small">
                    Esqueceste-te da palavra-passe?
                  </a>
                </div>

                {/* Back Button */}
                <div className="text-center">
                  <button
                    type="button"
                    className="btn btn-outline-secondary"
                    onClick={handleBackToLogin}
                    disabled={isLoading}
                  >
                    <i className="fas fa-arrow-left me-2"></i>
                    Voltar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdultLoginPage;