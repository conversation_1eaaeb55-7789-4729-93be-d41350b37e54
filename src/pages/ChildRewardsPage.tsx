import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Reward } from '../types/schema';
import { RewardStatus, ChildDashboardSection } from '../types/enums';
import { mockRewards, mockRewardCategories } from '../data/RewardMockData';
import ChildMobileNavbar from '../components/ChildMobileNavbar';

/**
 * ChildRewardsPage component - Child-friendly rewards page
 * Allows children to view and claim available rewards
 */
const ChildRewardsPage: React.FC = () => {
  const navigate = useNavigate();
  
  // Mock current child user
  const currentChild = {
    id: 'child-123',
    name: '<PERSON>',
    points: 85, // Current points available
    avatar: '👦'
  };

  const [rewards, setRewards] = useState<Reward[]>(mockRewards);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showClaimModal, setShowClaimModal] = useState(false);
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);

  // Filter rewards available for claiming
  const availableRewards = useMemo(() => {
    return rewards.filter(reward => 
      reward.status === RewardStatus.ACTIVE &&
      (selectedCategory === 'all' || reward.category.id === selectedCategory)
    );
  }, [rewards, selectedCategory]);

  // Handle reward claim
  const handleClaimReward = (reward: Reward) => {
    if (currentChild.points >= reward.pointsRequired) {
      setSelectedReward(reward);
      setShowClaimModal(true);
    }
  };

  // Confirm claim
  const handleConfirmClaim = () => {
    if (selectedReward) {
      // Update reward status to claimed (pending approval)
      setRewards(prev => prev.map(r => 
        r.id === selectedReward.id 
          ? { ...r, status: RewardStatus.CLAIMED, claimedAt: new Date() }
          : r
      ));
      
      setShowClaimModal(false);
      setSelectedReward(null);
      
      // Show success message
      alert('🎉 Recompensa reclamada! Aguarda aprovação dos pais.');
    }
  };

  return (
    <div className="child-rewards-page">
      {/* Header */}
      <div className="child-rewards-header">
        <div className="container-fluid px-3 py-4">
          <div className="d-flex align-items-center justify-content-between">
            <div className="d-flex align-items-center">
              <button 
                className="child-back-btn me-3"
                onClick={() => navigate('/child-dashboard')}
              >
                <i className="fas fa-arrow-left"></i>
              </button>
              <div>
                <h2 className="child-page-title mb-1">
                  🎁 Loja de Recompensas
                </h2>
                <p className="child-page-subtitle mb-0">
                  Tens <strong>{currentChild.points} pontos</strong> para gastar!
                </p>
              </div>
            </div>
            <div className="child-points-display">
              <div className="points-badge">
                <i className="fas fa-star"></i>
                <span>{currentChild.points}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Categories Filter */}
      <div className="child-categories-section">
        <div className="container-fluid px-3">
          <div className="categories-scroll">
            <button
              className={`category-btn ${selectedCategory === 'all' ? 'active' : ''}`}
              onClick={() => setSelectedCategory('all')}
            >
              <span className="category-icon">🎯</span>
              <span className="category-name">Todas</span>
            </button>
            {mockRewardCategories.map(category => (
              <button
                key={category.id}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category.id)}
              >
                <span className="category-icon">{category.icon}</span>
                <span className="category-name">{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Rewards Grid */}
      <div className="child-rewards-content">
        <div className="container-fluid px-3">
          {availableRewards.length === 0 ? (
            <div className="empty-state">
              <div className="empty-icon">😔</div>
              <h4>Nenhuma recompensa disponível</h4>
              <p>Completa mais tarefas para desbloquear recompensas!</p>
            </div>
          ) : (
            <div className="rewards-grid">
              {availableRewards.map(reward => {
                const canAfford = currentChild.points >= reward.pointsRequired;
                
                return (
                  <div 
                    key={reward.id} 
                    className={`reward-card ${!canAfford ? 'disabled' : ''}`}
                    onClick={() => canAfford && handleClaimReward(reward)}
                  >
                    <div className="reward-header">
                      <div className="reward-category-badge" style={{ backgroundColor: reward.category.color }}>
                        {reward.category.icon}
                      </div>
                      <div className="reward-points">
                        <i className="fas fa-star"></i>
                        {reward.pointsRequired}
                      </div>
                    </div>
                    
                    <div className="reward-content">
                      <h4 className="reward-title">{reward.title}</h4>
                      <p className="reward-description">{reward.description}</p>
                    </div>
                    
                    <div className="reward-footer">
                      {canAfford ? (
                        <button className="claim-btn">
                          <i className="fas fa-gift me-2"></i>
                          Reclamar
                        </button>
                      ) : (
                        <div className="insufficient-points">
                          <i className="fas fa-lock me-2"></i>
                          Precisas de mais {reward.pointsRequired - currentChild.points} pontos
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Claim Confirmation Modal */}
      {showClaimModal && selectedReward && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content child-modal">
              <div className="modal-header">
                <h5 className="modal-title">
                  🎁 Reclamar Recompensa
                </h5>
              </div>
              <div className="modal-body text-center">
                <div className="reward-preview">
                  <div className="reward-icon" style={{ backgroundColor: selectedReward.category.color }}>
                    {selectedReward.category.icon}
                  </div>
                  <h4>{selectedReward.title}</h4>
                  <p>{selectedReward.description}</p>
                  <div className="points-cost">
                    <i className="fas fa-star"></i>
                    {selectedReward.pointsRequired} pontos
                  </div>
                </div>
                <p className="confirmation-text">
                  Queres reclamar esta recompensa? Os teus pais vão aprovar!
                </p>
              </div>
              <div className="modal-footer">
                <button
                  className="btn btn-secondary"
                  onClick={() => setShowClaimModal(false)}
                >
                  Cancelar
                </button>
                <button
                  className="btn btn-primary"
                  onClick={handleConfirmClaim}
                >
                  <i className="fas fa-gift me-2"></i>
                  Sim, reclamar!
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Navigation */}
      <ChildMobileNavbar
        activeSection={ChildDashboardSection.REWARDS}
        onSectionChange={(section) => {
          if (section === ChildDashboardSection.DASHBOARD) navigate('/child-dashboard');
          if (section === ChildDashboardSection.TASKS) navigate('/child-tasks');
          if (section === ChildDashboardSection.CHAT) navigate('/child-chat-adult-style');
          if (section === ChildDashboardSection.TROPHIES) navigate('/child-trophies');
        }}
        unreadCount={0}
      />
    </div>
  );
};

export default ChildRewardsPage;
