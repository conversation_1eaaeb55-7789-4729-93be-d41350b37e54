import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChildDashboardSection } from '../types/enums';
import { mockChildUser, mockChildDashboardStats, mockChildNavigationCards } from '../data/ChildDashboardMockData';

/**
 * ChildDashboardPage component - Main dashboard for child users
 * Features colorful, engaging design with gaming elements
 */
const ChildDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState<ChildDashboardSection>(ChildDashboardSection.HOME);

  const handleCardClick = (route: string) => {
    console.log('Navigating to:', route);
    navigate(route);
  };

  const handleSectionChange = (section: ChildDashboardSection) => {
    setActiveSection(section);
    console.log('Section changed to:', section);
    
    // Handle navigation based on section
    switch (section) {
      case ChildDashboardSection.HOME:
        // Stay on dashboard
        break;
      case ChildDashboardSection.MY_TASKS:
        navigate('/child-tasks');
        break;
      case ChildDashboardSection.MY_TROPHIES:
        navigate('/child-trophies');
        break;
      case ChildDashboardSection.MY_REWARDS:
        navigate('/child-rewards');
        break;
      case ChildDashboardSection.MY_THEMES:
        navigate('/child-themes');
        break;
      case ChildDashboardSection.FAMILY_CHAT:
        navigate('/child-chat-adult-style');
        break;
      case ChildDashboardSection.SETTINGS:
        navigate('/child-settings');
        break;
    }
  };

  return (
    <div className="child-dashboard">
      {/* Header */}
      <div className="child-header text-center p-3 mb-3">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-auto">
              <div className="child-icon-large">👦</div>
            </div>
            <div className="col">
              <h2 className="child-title mb-1">Olá, {mockChildUser.name}!</h2>
              <p className="mb-0 text-muted small">
                Nível {mockChildUser.level} • {mockChildDashboardStats.totalPoints} pontos
              </p>
            </div>
            <div className="col-auto">
              <button 
                className="btn btn-outline-danger btn-sm"
                onClick={() => navigate('/')}
                title="Sair"
              >
                <i className="fas fa-sign-out-alt"></i>
                <span className="d-none d-sm-inline ms-1">Sair</span>
              </button>
            </div>
            <div className="col-12 col-md-auto mt-2 mt-md-0">
              <div className="badge bg-warning text-dark">
                🔥 {mockChildDashboardStats.streak} dias seguidos!
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container">
        {/* Debug Info */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="alert alert-info">
              <h5>🔧 Debug Info</h5>
              <p>Child: {mockChildUser.name}</p>
              <p>Points: {mockChildDashboardStats.totalPoints}</p>
              <p>Level: {mockChildUser.level}</p>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="child-stats-overview card mb-4">
          <div className="card-body">
            <h5 className="card-title text-center mb-4">
              <i className="fas fa-chart-line me-2"></i>
              As Minhas Estatísticas
            </h5>
            <div className="row g-2">
              <div className="col-6 col-md-3">
                <div className="child-stat-item">
                  <div className="child-stat-number text-primary">
                    {mockChildDashboardStats.completedTasks}
                  </div>
                  <div className="child-stat-label">Concluídas</div>
                </div>
              </div>
              <div className="col-6 col-md-3">
                <div className="child-stat-item">
                  <div className="child-stat-number text-warning">
                    {mockChildDashboardStats.pendingTasks}
                  </div>
                  <div className="child-stat-label">Pendentes</div>
                </div>
              </div>
              <div className="col-6 col-md-3">
                <div className="child-stat-item">
                  <div className="child-stat-number text-success">
                    {mockChildDashboardStats.achievedTrophies}
                  </div>
                  <div className="child-stat-label">Troféus</div>
                </div>
              </div>
              <div className="col-6 col-md-3">
                <div className="child-stat-item">
                  <div className="child-stat-number text-info">
                    {mockChildDashboardStats.availableRewards}
                  </div>
                  <div className="child-stat-label">Recompensas</div>
                </div>
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className="mt-4">
              <div className="d-flex justify-content-between mb-2">
                <span className="fw-bold">Progresso Semanal</span>
                <span className="text-muted">{mockChildDashboardStats.weeklyProgress}%</span>
              </div>
              <div className="progress" style={{ height: '20px' }}>
                <div 
                  className="progress-bar bg-gradient" 
                  style={{ 
                    width: `${mockChildDashboardStats.weeklyProgress}%`,
                    background: 'linear-gradient(90deg, #ff6b35, #ffd700, #4caf50)'
                  }}
                  role="progressbar"
                >
                  {mockChildDashboardStats.weeklyProgress}%
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Cards */}
        <div className="row mb-4">
          <div className="col-12">
            <h5 className="mb-3">
              <i className="fas fa-th-large me-2"></i>
              O Que Queres Fazer?
            </h5>
          </div>
        </div>

        <div className="row g-2 mb-4">
          {mockChildNavigationCards.map((card) => (
            <div key={card.id} className="col-6 col-md-4 col-lg-3">
              <div 
                className="child-navigation-card card h-100"
                onClick={() => handleCardClick(card.route)}
                style={{ cursor: 'pointer' }}
              >
                <div className="card-body text-center p-3">
                  <div className="child-card-icon mb-2">{card.icon}</div>
                  <h6 className="child-card-title mb-1">{card.title}</h6>
                  <p className="child-card-description small mb-0">{card.description}</p>
                  {card.count > 0 && (
                    <div className="child-card-count-badge">
                      {card.count}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="row mb-4">
          <div className="col-12">
            <h5 className="mb-3">
              <i className="fas fa-bolt me-2"></i>
              Ações Rápidas
            </h5>
          </div>
        </div>

        <div className="row g-2 mb-4">
          <div className="col-6 col-md-3">
            <button 
              className="btn btn-primary w-100 py-2"
              onClick={() => handleCardClick('/child-tasks')}
            >
              <i className="fas fa-tasks me-1"></i>
              <span className="d-none d-sm-inline">Ver </span>Tarefas
            </button>
          </div>
          <div className="col-6 col-md-3">
            <button 
              className="btn btn-success w-100 py-2"
              onClick={() => handleCardClick('/child-rewards')}
            >
              <i className="fas fa-gift me-1"></i>
              Recompensas
            </button>
          </div>
          <div className="col-6 col-md-3">
            <button 
              className="btn btn-warning w-100 py-2"
              onClick={() => handleCardClick('/child-trophies')}
            >
              <i className="fas fa-trophy me-1"></i>
              Troféus
            </button>
          </div>
          <div className="col-6 col-md-3">
            <button 
              className="btn btn-info w-100 py-2"
              onClick={() => handleCardClick('/child-chat')}
            >
              <i className="fas fa-comments me-1"></i>
              Chat
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navbar */}
      <div className="child-mobile-navbar d-md-none position-fixed" style={{ bottom: 0, left: 0, right: 0, zIndex: 1000, backgroundColor: '#ffffff' }}>
        <div className="container">
          <div className="row">
            <div className="col">
              <button 
                className={`child-mobile-nav-item btn w-100 ${activeSection === ChildDashboardSection.HOME ? 'active' : ''}`}
                onClick={() => handleSectionChange(ChildDashboardSection.HOME)}
              >
                <div className="child-mobile-nav-icon">🏠</div>
                <div className="child-mobile-nav-label">Início</div>
              </button>
            </div>
            <div className="col">
              <button 
                className={`child-mobile-nav-item btn w-100 ${activeSection === ChildDashboardSection.MY_TASKS ? 'active' : ''}`}
                onClick={() => handleSectionChange(ChildDashboardSection.MY_TASKS)}
              >
                <div className="child-mobile-nav-icon">📋</div>
                <div className="child-mobile-nav-label">Tarefas</div>
              </button>
            </div>
            <div className="col">
              <button 
                className={`child-mobile-nav-item btn w-100 ${activeSection === ChildDashboardSection.MY_REWARDS ? 'active' : ''}`}
                onClick={() => handleSectionChange(ChildDashboardSection.MY_REWARDS)}
              >
                <div className="child-mobile-nav-icon">🎁</div>
                <div className="child-mobile-nav-label">Recompensas</div>
              </button>
            </div>
            <div className="col">
              <button 
                className={`child-mobile-nav-item btn w-100 ${activeSection === ChildDashboardSection.FAMILY_CHAT ? 'active' : ''}`}
                onClick={() => handleSectionChange(ChildDashboardSection.FAMILY_CHAT)}
              >
                <div className="child-mobile-nav-icon">💬</div>
                <div className="child-mobile-nav-label">Chat</div>
                <div className="child-mobile-nav-badge">2</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChildDashboardPage;