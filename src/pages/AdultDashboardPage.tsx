import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import DashboardHeader from '../components/DashboardHeader';
import NavigationCardsGrid from '../components/NavigationCardsGrid';
import StatsOverview from '../components/StatsOverview';
import MobileNavbar from '../components/MobileNavbar';
import { DashboardSection } from '../types/enums';
import { mockDashboardStats, mockNavigationCards, mockRootProps } from '../data/AdultDashboardMockData';

/**
 * AdultDashboardPage component - Main dashboard for adult users
 * Features Nintendo-style gaming design with navigation cards and mobile navbar
 */
const AdultDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser, userProfile, logout } = useAuth();
  const [activeSection, setActiveSection] = useState<DashboardSection>(DashboardSection.HOME);

  const handleCardClick = (route: string) => {
    console.log('Navigating to:', route);
    navigate(route);
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/adult-login');
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const handleSectionChange = (section: DashboardSection) => {
    setActiveSection(section);
    console.log('Section changed to:', section);
    
    // Handle navigation based on section
    switch (section) {
      case DashboardSection.HOME:
        // Stay on dashboard
        break;
      case DashboardSection.TASKS:
        console.log('Navigate to tasks');
        navigate('/parent-tasks');
        break;
      case DashboardSection.CHAT:
        console.log('Navigate to chat');
        navigate('/chat');
        break;
      default:
        break;
    }
  };

  const handleBackToLogin = () => {
    navigate('/');
  };

  return (
    <div className="adult-dashboard">
      {/* Header */}
      <DashboardHeader
        user={{
          id: currentUser?.uid || 'user-1',
          name: userProfile?.displayName || currentUser?.displayName || 'Utilizador',
          email: currentUser?.email || '<EMAIL>',
          avatar: '👨‍💼',
          role: 'adult'
        }}
        onLogout={handleLogout}
      />
      
      {/* Main Content */}
      <div className="dashboard-main">
        <div className="container">
          {/* Welcome Message */}
          <div className="row mb-4">
            <div className="col-12">
              <div className="text-center">
                <h2 className="display-6 fw-bold mb-2" style={{ color: 'var(--st-blue)' }}>
                  🎮 Bem-vindo à SuperTarefa! 🎮
                </h2>
                <p className="lead text-muted mb-4">
                  Gerir a família nunca foi tão fácil
                </p>
              </div>
            </div>
          </div>

          {/* Statistics Overview */}
          <div className="row mb-5">
            <div className="col-12">
              <StatsOverview stats={mockDashboardStats} />
            </div>
          </div>

          {/* Navigation Cards */}
          <div className="row mb-5">
            <div className="col-12">
              <div className="d-flex justify-content-between align-items-center mb-4">
                <h3 className="fw-bold mb-0" style={{ color: 'var(--st-blue)' }}>
                  <i className="fas fa-th-large me-2"></i>
                  Navegação Rápida
                </h3>
              </div>
              <NavigationCardsGrid 
                cards={mockNavigationCards}
                onCardClick={handleCardClick}
              />
            </div>
          </div>

          {/* Quick Actions */}
          <div className="row mb-5">
            <div className="col-12">
              <div className="card">
                <div className="card-body text-center py-4">
                  <h5 className="card-title mb-3">Ações Rápidas</h5>
                  <div className="d-flex gap-3 justify-content-center flex-wrap">
                    <button 
                      className="btn btn-primary"
                      onClick={() => navigate('/parent-tasks')}
                    >
                      <i className="fas fa-plus me-2"></i>
                      Adicionar Tarefa
                    </button>
                    <button 
                      className="btn btn-outline-secondary"
                      onClick={() => navigate('/parent-tasks')}
                    >
                      <i className="fas fa-list me-2"></i>
                      Ver Todas
                    </button>
                    <button 
                      className="btn btn-outline-secondary"
                      onClick={handleBackToLogin}
                    >
                      <i className="fas fa-sign-out-alt me-2"></i>
                      Sair
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navbar */}
      <MobileNavbar
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
        unreadCount={mockDashboardStats.unreadMessages}
      />
    </div>
  );
};

export default AdultDashboardPage;