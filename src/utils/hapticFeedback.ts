/**
 * Utility functions for haptic feedback on mobile devices
 */

export enum HapticType {
  LIGHT = 'light',
  MEDIUM = 'medium',
  HEAVY = 'heavy',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error'
}

/**
 * Provides haptic feedback if available on the device
 */
export const hapticFeedback = (type: HapticType = HapticType.LIGHT) => {
  // Check if the device supports haptic feedback
  if (!navigator.vibrate) {
    return;
  }

  let pattern: number | number[];

  switch (type) {
    case HapticType.LIGHT:
      pattern = 10;
      break;
    case HapticType.MEDIUM:
      pattern = 20;
      break;
    case HapticType.HEAVY:
      pattern = 50;
      break;
    case HapticType.SUCCESS:
      pattern = [10, 50, 10];
      break;
    case HapticType.WARNING:
      pattern = [20, 100, 20];
      break;
    case HapticType.ERROR:
      pattern = [50, 100, 50, 100, 50];
      break;
    default:
      pattern = 10;
  }

  navigator.vibrate(pattern);
};

/**
 * Provides haptic feedback for button press
 */
export const buttonPressHaptic = () => {
  hapticFeedback(HapticType.LIGHT);
};

/**
 * Provides haptic feedback for successful action
 */
export const successHaptic = () => {
  hapticFeedback(HapticType.SUCCESS);
};

/**
 * Provides haptic feedback for error
 */
export const errorHaptic = () => {
  hapticFeedback(HapticType.ERROR);
};

/**
 * Provides haptic feedback for swipe gesture
 */
export const swipeHaptic = () => {
  hapticFeedback(HapticType.MEDIUM);
};
