import React, { Suspense } from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import HomePage from '../pages/HomePage';
import LoginPage from '../pages/LoginPage';
import ChildLoginPage from '../pages/ChildLoginPage';
import AdultLoginPage from '../pages/AdultLoginPage';
import ParentTasksPage from '../pages/ParentTasksPage';
import AdultRegisterPage from '../pages/AdultRegisterPage';
import FamilySetupPage from '../pages/FamilySetupPage';
import AdultDashboardPage from '../pages/AdultDashboardPage';
import ChatPage from '../pages/ChatPage';
import AdultCalendarPage from '../pages/AdultCalendarPage';
import AdultRewardsPage from '../pages/AdultRewardsPage';
import AdultTrophiesPage from '../pages/AdultTrophiesPage';
import AdultShopPage from '../pages/AdultShopPage';
import ChildDashboardPage from '../pages/ChildDashboardPage';
import ChildTasksPage from '../pages/ChildTasksPage';
import ChildChatPage from '../pages/ChildChatPage';
import ChildChatPageAdultStyle from '../pages/ChildChatPageAdultStyle';

/**
 * Router configuration for the SuperTarefa application
 * Defines all available routes and their corresponding components
 */
const router = createBrowserRouter([
  {
    path: '/',
    element: <LoginPage />,
  },
  {
    path: '/child-login',
    element: <ChildLoginPage />,
  },
  {
    path: '/adult-login',
    element: <AdultLoginPage />,
  },
  {
    path: '/adult-register',
    element: <AdultRegisterPage />,
  },
  {
    path: '/family-setup',
    element: <FamilySetupPage />,
  },
  {
    path: '/home',
    element: <HomePage />,
  },
  {
    path: '/child-dashboard',
    element: <ChildDashboardPage />,
  },
  {
    path: '/child-tasks',
    element: <ChildTasksPage />,
  },
  {
    path: '/child-chat',
    element: <ChildChatPage />,
  },
  {
    path: '/child-chat-adult-style',
    element: <ChildChatPageAdultStyle />,
  },
  {
    path: '/adult-dashboard', 
    element: <AdultDashboardPage />,
  },
  {
    path: '/chat',
    element: <ChatPage />,
  },
  {
    path: '/parent-tasks',
    element: <ParentTasksPage />,
  },
  {
    path: '/adult-calendar',
    element: <AdultCalendarPage />,
  },
  {
    path: '/adult-rewards',
    element: <AdultRewardsPage />,
  },
      {
        path: '/adult-trophies',
        element: <AdultTrophiesPage />,
      },
      {
        path: '/adult-shop',
        element: <AdultShopPage />,
      },
      {
        path: '/child-dashboard',
        element: <ChildDashboardPage />,
      },
  // Fallback route
  {
    path: '*',
    element: <LoginPage />,
  },
]);

/**
 * AppRouter component - Provides routing functionality to the application
 * Wraps the router with Suspense for lazy loading support
 */
const AppRouter: React.FC = () => {
  return (
    <Suspense fallback={<div className="text-center p-5">A carregar...</div>}>
      <RouterProvider router={router} />
    </Suspense>
  );
};

export default AppRouter;