import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  serverTimestamp,
  Timestamp,
  writeBatch,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { useAuth } from './AuthContext';
import { ChatRoom, ChatMessage, ChatContextType, TypingIndicator, ChatParticipant } from '../types/chat';

// Create context
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Custom hook to use chat context
export const useChat = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

// Chat provider component
interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const { currentUser, userProfile } = useAuth();
  
  // State
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [messages, setMessages] = useState<{ [chatId: string]: ChatMessage[] }>({});
  const [typingUsers, setTypingUsers] = useState<{ [chatId: string]: TypingIndicator[] }>({});
  const [loading, setLoading] = useState(false);

  // Typing timeout refs
  const typingTimeouts = React.useRef<{ [chatId: string]: NodeJS.Timeout }>({});

  // Create chat room
  const createChatRoom = async (name: string, type: ChatRoom['type'], participants: string[]): Promise<string> => {
    if (!currentUser) throw new Error('User not authenticated');

    try {
      const now = new Date();
      const chatRoom: Omit<ChatRoom, 'id'> = {
        name,
        type,
        participants: [currentUser.uid, ...participants],
        participantDetails: [], // Will be populated by cloud function or client
        createdAt: now as any,
        updatedAt: now as any,
        createdBy: currentUser.uid,
        isActive: true,
        unreadCounts: {}
      };

      const docRef = await addDoc(collection(db, 'chatRooms'), chatRoom);
      return docRef.id;
    } catch (error) {
      console.error('Error creating chat room:', error);
      throw error;
    }
  };

  // Join chat room
  const joinChatRoom = async (chatId: string): Promise<void> => {
    if (!currentUser) throw new Error('User not authenticated');

    const chatRef = doc(db, 'chatRooms', chatId);
    await updateDoc(chatRef, {
      participants: arrayUnion(currentUser.uid),
      updatedAt: serverTimestamp()
    });
  };

  // Leave chat room
  const leaveChatRoom = async (chatId: string): Promise<void> => {
    if (!currentUser) throw new Error('User not authenticated');

    const chatRef = doc(db, 'chatRooms', chatId);
    await updateDoc(chatRef, {
      participants: arrayRemove(currentUser.uid),
      updatedAt: serverTimestamp()
    });
  };

  // Send message
  const sendMessage = async (chatId: string, content: string, type: ChatMessage['type'] = 'text'): Promise<void> => {
    if (!currentUser || !userProfile) throw new Error('User not authenticated');

    try {
      const now = new Date();
      const message: Omit<ChatMessage, 'id'> = {
        chatId,
        senderId: currentUser.uid,
        senderName: userProfile.displayName,
        senderAvatar: userProfile.role === 'adult' ? '👨‍💼' : '👦',
        content,
        type,
        timestamp: now as any, // Use regular Date for now
        status: 'sent'
      };

      // Add message to messages collection
      await addDoc(collection(db, 'messages'), message);

      // Update chat room's last message
      const chatRef = doc(db, 'chatRooms', chatId);
      await updateDoc(chatRef, {
        lastMessage: {
          content,
          senderId: currentUser.uid,
          timestamp: now
        },
        updatedAt: now
      });

      // Stop typing indicator
      stopTyping(chatId);
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  };

  // Edit message
  const editMessage = async (messageId: string, newContent: string): Promise<void> => {
    const messageRef = doc(db, 'messages', messageId);
    await updateDoc(messageRef, {
      content: newContent,
      edited: true,
      editedAt: serverTimestamp()
    });
  };

  // Delete message
  const deleteMessage = async (messageId: string): Promise<void> => {
    await deleteDoc(doc(db, 'messages', messageId));
  };

  // Mark message as read
  const markAsRead = async (chatId: string, messageId: string): Promise<void> => {
    if (!currentUser) return;

    const messageRef = doc(db, 'messages', messageId);
    await updateDoc(messageRef, {
      status: 'read'
    });

    // Reset unread count for this user in this chat
    const chatRef = doc(db, 'chatRooms', chatId);
    await updateDoc(chatRef, {
      [`unreadCounts.${currentUser.uid}`]: 0
    });
  };

  // Start typing indicator
  const startTyping = (chatId: string) => {
    if (!currentUser || !userProfile) return;

    // Clear existing timeout
    if (typingTimeouts.current[chatId]) {
      clearTimeout(typingTimeouts.current[chatId]);
    }

    // Create typing indicator document
    const typingData = {
      chatId,
      userId: currentUser.uid,
      userName: userProfile.displayName,
      timestamp: serverTimestamp()
    };

    // Try to create the document directly
    addDoc(collection(db, 'typing'), typingData).catch((error) => {
      console.log('Typing indicator error (non-critical):', error.message);
    });

    // Auto-stop typing after 3 seconds
    typingTimeouts.current[chatId] = setTimeout(() => {
      stopTyping(chatId);
    }, 3000);
  };

  // Stop typing indicator
  const stopTyping = (chatId: string) => {
    if (!currentUser) return;

    // Clear timeout
    if (typingTimeouts.current[chatId]) {
      clearTimeout(typingTimeouts.current[chatId]);
      delete typingTimeouts.current[chatId];
    }

    // Remove typing indicator by querying and deleting
    const typingQuery = query(
      collection(db, 'typing'),
      where('chatId', '==', chatId),
      where('userId', '==', currentUser.uid)
    );

    // Note: We'll handle this more gracefully in a real implementation
    // For now, we'll just log that typing stopped
    console.log('Typing stopped for user:', currentUser.uid, 'in chat:', chatId);
  };

  // Subscribe to chat messages
  const subscribeToChat = (chatId: string) => {
    const messagesQuery = query(
      collection(db, 'messages'),
      where('chatId', '==', chatId),
      orderBy('timestamp', 'asc')
    );

    return onSnapshot(messagesQuery, (snapshot) => {
      const chatMessages: ChatMessage[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        // Handle different timestamp formats
        let timestamp = data.timestamp;
        if (timestamp && timestamp.toDate) {
          // Firestore Timestamp
          timestamp = timestamp;
        } else if (timestamp && timestamp.seconds) {
          // Firestore Timestamp object
          timestamp = new Date(timestamp.seconds * 1000);
        } else if (timestamp instanceof Date) {
          // Regular Date
          timestamp = timestamp;
        } else {
          // Fallback to current time
          timestamp = new Date();
        }

        chatMessages.push({
          id: doc.id,
          ...data,
          timestamp
        } as ChatMessage);
      });

      setMessages(prev => ({
        ...prev,
        [chatId]: chatMessages
      }));
    }, (error) => {
      console.error('Error subscribing to chat messages:', error);
    });
  };

  // Subscribe to user's chat rooms
  const subscribeToUserChats = () => {
    if (!currentUser) return () => {};

    // Use fallback query while composite index is building
    // TODO: Switch back to optimized query once index is ready
    const chatsQuery = query(
      collection(db, 'chatRooms'),
      where('participants', 'array-contains', currentUser.uid)
    );

    return onSnapshot(chatsQuery, (snapshot) => {
      const userChatRooms: ChatRoom[] = [];
      snapshot.forEach((doc) => {
        const chatData = { id: doc.id, ...doc.data() } as ChatRoom;
        // Filter active chats on the client side
        if (chatData.isActive !== false) {
          userChatRooms.push(chatData);
        }
      });

      // Sort by updatedAt on the client side
      userChatRooms.sort((a, b) => {
        let aTime = 0;
        let bTime = 0;

        if (a.updatedAt) {
          if (a.updatedAt.toDate) {
            aTime = a.updatedAt.toDate().getTime();
          } else if (a.updatedAt instanceof Date) {
            aTime = a.updatedAt.getTime();
          }
        }

        if (b.updatedAt) {
          if (b.updatedAt.toDate) {
            bTime = b.updatedAt.toDate().getTime();
          } else if (b.updatedAt instanceof Date) {
            bTime = b.updatedAt.getTime();
          }
        }

        return bTime - aTime;
      });

      setChatRooms(userChatRooms);
    }, (error) => {
      console.error('Chat subscription error:', error);
      // If query fails, set empty array
      setChatRooms([]);
    });
  };

  // Subscribe to typing indicators
  const subscribeToTyping = (chatId: string) => {
    const typingQuery = query(
      collection(db, 'typing'),
      where('chatId', '==', chatId)
    );

    return onSnapshot(typingQuery, (snapshot) => {
      const indicators: TypingIndicator[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data() as TypingIndicator;
        // Only show typing indicators from other users
        if (data.userId !== currentUser?.uid) {
          indicators.push(data);
        }
      });

      setTypingUsers(prev => ({
        ...prev,
        [chatId]: indicators
      }));
    });
  };

  // Initialize chat subscriptions
  useEffect(() => {
    if (!currentUser) return;

    const unsubscribeChats = subscribeToUserChats();

    return () => {
      unsubscribeChats();
    };
  }, [currentUser]);

  // Subscribe to current chat
  useEffect(() => {
    if (!currentChatId) return;

    const unsubscribeMessages = subscribeToChat(currentChatId);
    const unsubscribeTyping = subscribeToTyping(currentChatId);

    return () => {
      unsubscribeMessages();
      unsubscribeTyping();
    };
  }, [currentChatId]);

  const value: ChatContextType = {
    currentChatId,
    chatRooms,
    messages,
    typingUsers,
    loading,
    createChatRoom,
    joinChatRoom,
    leaveChatRoom,
    sendMessage,
    editMessage,
    deleteMessage,
    markAsRead,
    startTyping,
    stopTyping,
    subscribeToChat,
    subscribeToUserChats
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

export default ChatContext;
