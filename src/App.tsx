import { AuthProvider } from './contexts/AuthContext';
import { Chat<PERSON>rovider } from './contexts/ChatContext';
import AppRouter from './router/index';
import './App.css';

/**
 * Main App component - Entry point for the SuperTarefa application
 * Provides routing functionality and global application structure
 */
function App() {
  return (
    <AuthProvider>
      <ChatProvider>
        <div className="App">
          <AppRouter />
        </div>
      </ChatProvider>
    </AuthProvider>
  );
}

export default App;
