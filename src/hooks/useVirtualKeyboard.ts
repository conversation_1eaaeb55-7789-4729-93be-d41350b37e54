import { useState, useEffect } from 'react';

/**
 * Hook to detect virtual keyboard state on mobile devices
 * Returns whether the virtual keyboard is open and the viewport height
 */
export const useVirtualKeyboard = () => {
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const [viewportHeight, setViewportHeight] = useState(window.innerHeight);
  const [initialHeight] = useState(window.innerHeight);

  useEffect(() => {
    const handleResize = () => {
      const currentHeight = window.innerHeight;
      setViewportHeight(currentHeight);
      
      // Consider keyboard open if height decreased by more than 150px
      const heightDifference = initialHeight - currentHeight;
      setIsKeyboardOpen(heightDifference > 150);
    };

    const handleVisualViewportChange = () => {
      if (window.visualViewport) {
        const currentHeight = window.visualViewport.height;
        setViewportHeight(currentHeight);
        
        const heightDifference = initialHeight - currentHeight;
        setIsKeyboardOpen(heightDifference > 150);
      }
    };

    // Listen to window resize
    window.addEventListener('resize', handleResize);
    
    // Listen to visual viewport changes (better for mobile)
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleVisualViewportChange);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleVisualViewportChange);
      }
    };
  }, [initialHeight]);

  return {
    isKeyboardOpen,
    viewportHeight,
    keyboardHeight: initialHeight - viewportHeight
  };
};
