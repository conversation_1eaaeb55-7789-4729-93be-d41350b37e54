import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import ChildiMessageBubble from './ChildiMessageBubble';
import ChildiMessageInput from './ChildiMessageInput';
import { Chat, FamilyMember, ChatMessage } from '../types/schema';
import { MessageType, MessageStatus, CallType, ChatType } from '../types/enums';

interface ChildiMessageConversationProps {
  chat: Chat;
  currentUser: FamilyMember;
  onBack: () => void;
  onStartCall?: (type: CallType) => void;
}

/**
 * ChildiMessageConversation component - iMessage-style conversation for children
 * Features colorful, engaging design with fun animations and child-friendly interface
 */
const ChildiMessageConversation: React.FC<ChildiMessageConversationProps> = ({
  chat,
  currentUser,
  onBack,
  onStartCall
}) => {
  const navigate = useNavigate();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Mock messages for demonstration
  const mockMessages: ChatMessage[] = [
    {
      id: 'msg-1',
      senderId: 'sebastian-rudiger',
      senderName: 'Sebastian Rudiger',
      content: 'Hi, Jimmy! Any update today?',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      status: MessageStatus.READ,
    },
    {
      id: 'msg-2',
      senderId: currentUser.id,
      senderName: currentUser.name,
      content: 'All good! we have some update ✨',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 3.5 * 60 * 60 * 1000),
      status: MessageStatus.READ,
    },
    {
      id: 'msg-3',
      senderId: currentUser.id,
      senderName: currentUser.name,
      content: 'Here\'s the new landing page design!\nhttps://www.figma.com/file/EQJuT...',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 3.4 * 60 * 60 * 1000),
      status: MessageStatus.read,
    },
    {
      id: 'msg-4',
      senderId: 'sebastian-rudiger',
      senderName: 'Sebastian Rudiger',
      content: 'Cool! I have some feedbacks on the "How it work" section. but overall looks good now! 👍',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      status: MessageStatus.read,
    },
    {
      id: 'msg-5',
      senderId: currentUser.id,
      senderName: currentUser.name,
      content: 'Perfect! Will check it 👍',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000),
      status: MessageStatus.read,
    }
  ];

  useEffect(() => {
    // Simulate loading messages
    setTimeout(() => {
      setMessages(mockMessages);
      setIsLoading(false);
    }, 1000);
  }, [chat.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = useCallback((content: string) => {
    const newMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      senderId: currentUser.id,
      senderName: currentUser.name,
      content,
      type: MessageType.TEXT,
      timestamp: new Date(),
      status: MessageStatus.SENDING,
    };

    setMessages(prev => [...prev, newMessage]);

    // Simulate message sending
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: MessageStatus.SENT }
            : msg
        )
      );
    }, 500);

    // Simulate delivery
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: MessageStatus.DELIVERED }
            : msg
        )
      );
    }, 1000);
  }, [currentUser]);

  const getChatTitle = (): string => {
    if (chat.type === ChatType.FAMILY) {
      return 'Família 👨‍👩‍👧‍👦';
    }
    const otherParticipant = chat.participants.find(p => p.id !== currentUser.id);
    return otherParticipant?.name || 'Chat';
  };

  const getParticipantCount = (): number => {
    return chat.participants.length;
  };

  return (
    <div className="child-imessage-conversation">
      {/* Header */}
      <div className="child-imessage-header">
        <div className="container-fluid px-3">
          <div className="row align-items-center">
            <div className="col-auto">
              <button
                className="child-imessage-back-btn"
                onClick={onBack}
                title="Voltar"
              >
                <i className="fas fa-arrow-left"></i>
              </button>
            </div>

            <div className="col-auto">
              <div className="d-flex align-items-center">
                <img
                  src={`https://ui-avatars.com/api/?name=${encodeURIComponent(getChatTitle())}&background=6366f1&color=fff&size=40`}
                  alt={getChatTitle()}
                  className="conversation-avatar me-3"
                />
                <div>
                  <h6 className="child-imessage-chat-title mb-0">
                    {getChatTitle()}
                  </h6>
                  <small className="child-imessage-chat-subtitle">
                    {chat.type === ChatType.FAMILY
                      ? 'Online'
                      : 'Online'
                    }
                  </small>
                </div>
              </div>
            </div>

            <div className="col"></div>

            <div className="col-auto">
              <div className="child-imessage-call-buttons">
                {onStartCall && (
                  <>
                    <button
                      className="child-imessage-call-btn video me-2"
                      onClick={() => onStartCall(CallType.VIDEO)}
                      title="Chamada de vídeo"
                    >
                      <i className="fas fa-video"></i>
                    </button>
                    <button
                      className="child-imessage-call-btn audio"
                      onClick={() => onStartCall(CallType.AUDIO)}
                      title="Chamada de voz"
                    >
                      <i className="fas fa-phone"></i>
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Messages Container */}
      <div 
        ref={messagesContainerRef}
        className="child-imessage-messages-container"
      >
        {isLoading ? (
          <div className="child-imessage-loading">
            <div className="child-imessage-loading-animation">
              <div className="child-imessage-loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <p>A carregar mensagens... 💬</p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => {
              const showAvatar = index === 0 ||
                messages[index - 1].senderId !== message.senderId;

              return (
                <ChildiMessageBubble
                  key={message.id}
                  message={message}
                  currentUser={currentUser}
                  showAvatar={showAvatar}
                  chatType={chat.type}
                />
              );
            })}
            
            {isTyping && (
              <div className="child-imessage-typing-indicator">
                <div className="child-imessage-typing-bubble">
                  <div className="child-imessage-typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="child-imessage-input-fixed">
        <ChildiMessageInput
          onSendMessage={handleSendMessage}
          placeholder="Type here..."
        />
      </div>
    </div>
  );
};

export default ChildiMessageConversation;
