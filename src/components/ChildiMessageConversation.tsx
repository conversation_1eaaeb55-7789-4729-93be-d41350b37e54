import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import ChildiMessageBubble from './ChildiMessageBubble';
import ChildiMessageInput from './ChildiMessageInput';
import { Chat, FamilyMember, ChatMessage } from '../types/schema';
import { MessageType, MessageStatus, CallType, ChatType } from '../types/enums';

interface ChildiMessageConversationProps {
  chat: Chat;
  currentUser: FamilyMember;
  onBack: () => void;
  onStartCall?: (type: CallType) => void;
}

/**
 * ChildiMessageConversation component - iMessage-style conversation for children
 * Features colorful, engaging design with fun animations and child-friendly interface
 */
const ChildiMessageConversation: React.FC<ChildiMessageConversationProps> = ({
  chat,
  currentUser,
  onBack,
  onStartCall
}) => {
  const navigate = useNavigate();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Mock messages for demonstration
  const mockMessages: ChatMessage[] = [
    {
      id: 'msg-1',
      senderId: 'parent-1',
      senderName: 'Mãe',
      content: 'Olá João! Como foi a escola hoje? 😊',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      status: MessageStatus.READ,
    },
    {
      id: 'msg-2',
      senderId: currentUser.id,
      senderName: currentUser.name,
      content: 'Foi muito fixe! Aprendi sobre dinossauros! 🦕🦖',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000),
      status: MessageStatus.READ,
    },
    {
      id: 'msg-3',
      senderId: 'parent-2',
      senderName: 'Pai',
      content: 'Que giro! Qual foi o teu dinossauro favorito?',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
      status: MessageStatus.READ,
    },
    {
      id: 'msg-4',
      senderId: currentUser.id,
      senderName: currentUser.name,
      content: 'O Tiranossauro Rex! 🦖 É muito grande e forte!',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      status: MessageStatus.READ,
    },
    {
      id: 'msg-5',
      senderId: 'parent-1',
      senderName: 'Mãe',
      content: 'Não te esqueças de arrumar o quarto antes do jantar! 🏠✨',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      status: MessageStatus.READ,
    },
    {
      id: 'msg-6',
      senderId: currentUser.id,
      senderName: currentUser.name,
      content: 'Já arrumei! Posso jogar um bocadinho? 🎮',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      status: MessageStatus.DELIVERED,
    },
    {
      id: 'msg-7',
      senderId: 'parent-1',
      senderName: 'Mãe',
      content: 'Claro! Mas só até à hora do jantar! 😊🍽️',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 10 * 60 * 1000),
      status: MessageStatus.READ,
    },
    {
      id: 'msg-8',
      senderId: currentUser.id,
      senderName: currentUser.name,
      content: 'Obrigado mãe! És a melhor! ❤️👩',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      status: MessageStatus.READ,
    }
  ];

  useEffect(() => {
    // Simulate loading messages
    setTimeout(() => {
      setMessages(mockMessages);
      setIsLoading(false);
    }, 1000);
  }, [chat.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = useCallback((content: string) => {
    const newMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      senderId: currentUser.id,
      senderName: currentUser.name,
      content,
      type: MessageType.TEXT,
      timestamp: new Date(),
      status: MessageStatus.SENDING,
    };

    setMessages(prev => [...prev, newMessage]);

    // Simulate message sending
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: MessageStatus.SENT }
            : msg
        )
      );
    }, 500);

    // Simulate delivery
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: MessageStatus.DELIVERED }
            : msg
        )
      );
    }, 1000);
  }, [currentUser]);

  const getChatTitle = (): string => {
    if (chat.type === ChatType.FAMILY) {
      return 'Família 👨‍👩‍👧‍👦';
    }
    const otherParticipant = chat.participants.find(p => p.id !== currentUser.id);
    return otherParticipant?.name || 'Chat';
  };

  const getParticipantCount = (): number => {
    return chat.participants.length;
  };

  return (
    <div className="child-imessage-conversation">
      {/* Header */}
      <div className="child-imessage-header">
        <div className="container-fluid">
          <div className="row align-items-center">
            <div className="col-auto">
              <button
                className="child-imessage-back-btn"
                onClick={onBack}
                title="Voltar"
              >
                <span className="child-imessage-back-icon">←</span>
              </button>
            </div>
            
            <div className="col text-center">
              <div className="child-imessage-chat-info">
                <h4 className="child-imessage-chat-title">
                  {getChatTitle()}
                </h4>
                <p className="child-imessage-chat-subtitle">
                  {chat.type === ChatType.FAMILY 
                    ? `${getParticipantCount()} membros` 
                    : 'Online agora'
                  }
                </p>
              </div>
            </div>
            
            <div className="col-auto">
              <div className="child-imessage-call-buttons">
                {onStartCall && (
                  <>
                    <button
                      className="child-imessage-call-btn audio"
                      onClick={() => onStartCall(CallType.AUDIO)}
                      title="Chamada de voz"
                    >
                      📞
                    </button>
                    <button
                      className="child-imessage-call-btn video"
                      onClick={() => onStartCall(CallType.VIDEO)}
                      title="Chamada de vídeo"
                    >
                      📹
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Messages Container */}
      <div 
        ref={messagesContainerRef}
        className="child-imessage-messages-container"
      >
        {isLoading ? (
          <div className="child-imessage-loading">
            <div className="child-imessage-loading-animation">
              <div className="child-imessage-loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <p>A carregar mensagens... 💬</p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => {
              const showAvatar = index === 0 ||
                messages[index - 1].senderId !== message.senderId;

              return (
                <ChildiMessageBubble
                  key={message.id}
                  message={message}
                  currentUser={currentUser}
                  showAvatar={showAvatar}
                  chatType={chat.type}
                />
              );
            })}
            
            {isTyping && (
              <div className="child-imessage-typing-indicator">
                <div className="child-imessage-typing-bubble">
                  <div className="child-imessage-typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="child-imessage-input-fixed">
        <ChildiMessageInput
          onSendMessage={handleSendMessage}
          placeholder={`Mensagem para ${getChatTitle()}...`}
        />
      </div>
    </div>
  );
};

export default ChildiMessageConversation;
