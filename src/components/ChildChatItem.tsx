import React, { useState, useRef } from 'react';
import { Chat, FamilyMember } from '../types/schema';
import { ChatType, OnlineStatus } from '../types/enums';
import { formatMessageTime } from '../utils/formatters';
import { swipeHaptic, buttonPressHaptic } from '../utils/hapticFeedback';

interface ChildChatItemProps {
  chat: Chat;
  currentUser: FamilyMember;
  onChatSelect: (chatId: string) => void;
  onSwipeAction?: (chatId: string, action: string) => void;
}

/**
 * ChildChatItem component - Colorful chat item for children
 * Features gaming-style design with animations and haptic feedback
 */
const ChildChatItem: React.FC<ChildChatItemProps> = ({
  chat,
  currentUser,
  onChatSelect,
  onSwipeAction
}) => {
  const [isSwiped, setIsSwiped] = useState(false);
  const [startX, setStartX] = useState<number | null>(null);
  const itemRef = useRef<HTMLDivElement>(null);

  const getOtherParticipant = (): FamilyMember | null => {
    if (chat.type === ChatType.PRIVATE) {
      return chat.participants.find(p => p.id !== currentUser.id) || null;
    }
    return null;
  };

  const getChatTitle = (): string => {
    if (chat.type === ChatType.FAMILY) {
      return chat.name;
    }
    const otherParticipant = getOtherParticipant();
    return otherParticipant?.name || chat.name;
  };

  const getDisplayAvatar = (): string => {
    if (chat.type === ChatType.FAMILY) {
      return '👨‍👩‍👧‍👦';
    }
    const otherParticipant = getOtherParticipant();
    return otherParticipant?.avatar || '💬';
  };

  const getOnlineStatus = (): OnlineStatus | null => {
    if (chat.type === ChatType.PRIVATE) {
      const otherParticipant = getOtherParticipant();
      return otherParticipant?.onlineStatus || null;
    }
    return null;
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    setStartX(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!startX) return;
    
    const currentX = e.touches[0].clientX;
    const diffX = startX - currentX;
    
    // More sensitive swipe detection for better UX
    if (diffX > 30 && !isSwiped) {
      setIsSwiped(true);
      swipeHaptic();
    } else if (diffX < -30 && isSwiped) {
      setIsSwiped(false);
    }
  };

  const handleTouchEnd = () => {
    setStartX(null);
  };

  const handleClick = () => {
    if (!isSwiped) {
      buttonPressHaptic();
      onChatSelect(chat.id);
    }
  };

  const getLastMessagePreview = (): string => {
    if (!chat.lastMessage) return 'Sem mensagens';
    
    if (chat.lastMessage.content.length > 30) {
      return chat.lastMessage.content.substring(0, 30) + '...';
    }
    return chat.lastMessage.content;
  };

  const getSubtitle = (): string => {
    if (chat.type === ChatType.FAMILY) {
      return '🏠 Chat da família';
    }
    const onlineStatus = getOnlineStatus();
    return onlineStatus === OnlineStatus.ONLINE ? '🟢 Online' : '⚫ Offline';
  };

  return (
    <div 
      ref={itemRef}
      className="child-chat-item mobile-touch-target"
      onClick={handleClick}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{
        transform: isSwiped ? 'translateX(-80px)' : 'translateX(0)'
      }}
    >
      <div className="d-flex align-items-center">
        <div className="child-chat-item-avatar me-3">
          {getDisplayAvatar()}
        </div>
        
        <div className="flex-grow-1">
          <div className="d-flex justify-content-between align-items-start mb-1">
            <h6 className="child-chat-item-title">
              {getChatTitle()}
            </h6>
            
            {chat.lastMessage && (
              <small className="child-chat-item-time">
                {formatMessageTime(chat.lastMessage.timestamp)}
              </small>
            )}
          </div>
          
          <p className="child-chat-item-subtitle mb-1">
            {getSubtitle()}
          </p>
          
          {chat.lastMessage && (
            <p className="mb-0 text-muted small">
              {getLastMessagePreview()}
            </p>
          )}
        </div>
        
        {chat.unreadCount > 0 && (
          <div className="child-chat-item-unread ms-2">
            {chat.unreadCount > 9 ? '9+' : chat.unreadCount}
          </div>
        )}
      </div>
      
      {/* Swipe actions */}
      {isSwiped && (
        <div 
          className="position-absolute top-0 end-0 h-100 d-flex align-items-center px-3"
          style={{
            background: 'linear-gradient(135deg, var(--st-red) 0%, var(--st-orange) 100%)',
            color: 'white',
            borderRadius: '0 20px 20px 0',
            minWidth: '80px',
            justifyContent: 'center'
          }}
          onClick={(e) => {
            e.stopPropagation();
            onSwipeAction?.(chat.id, 'archive');
          }}
        >
          <i className="fas fa-archive"></i>
        </div>
      )}
    </div>
  );
};

export default ChildChatItem;
