import React, { useState, useRef, useEffect } from 'react';
import EmojiPicker from './EmojiPicker';

interface ChildiMessageInputProps {
  onSendMessage: (content: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

/**
 * ChildiMessageInput component - iMessage-style input for children
 * Features colorful design with emoji picker and fun animations
 */
const ChildiMessageInput: React.FC<ChildiMessageInputProps> = ({
  onSendMessage,
  placeholder = "Escreve uma mensagem...",
  disabled = false
}) => {
  const [message, setMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.style.height = 'auto';
      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 120)}px`;
    }
  }, [message]);

  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
      setIsTyping(false);
      
      // Add fun send animation
      const sendButton = document.querySelector('.child-imessage-send-btn');
      if (sendButton) {
        sendButton.classList.add('child-imessage-send-animation');
        setTimeout(() => {
          sendButton.classList.remove('child-imessage-send-animation');
        }, 300);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    setIsTyping(e.target.value.length > 0);
  };

  const handleEmojiSelect = (emoji: string) => {
    setMessage(prev => prev + emoji);
    setShowEmojiPicker(false);
    inputRef.current?.focus();
  };

  const quickEmojis = ['😊', '😂', '❤️', '👍', '🎉', '🔥', '😍', '🤔', '😢', '😮'];

  return (
    <div className="child-imessage-input-container">
      {/* Quick emoji bar */}
      <div className="child-imessage-quick-emojis">
        {quickEmojis.map((emoji, index) => (
          <button
            key={index}
            className="child-imessage-quick-emoji-btn"
            onClick={() => handleEmojiSelect(emoji)}
            type="button"
          >
            {emoji}
          </button>
        ))}
      </div>

      {/* Main input area */}
      <div className="child-imessage-input-wrapper">
        <div className="child-imessage-input-box">
          {/* Emoji picker button */}
          <button
            className="child-imessage-emoji-btn"
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            type="button"
            title="Adicionar emoji"
          >
            😊
          </button>

          {/* Text input */}
          <textarea
            ref={inputRef}
            className="child-imessage-input"
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            style={{
              resize: 'none',
              overflow: 'hidden'
            }}
          />

          {/* Send button */}
          <button
            className={`child-imessage-send-btn ${message.trim() ? 'active' : ''}`}
            onClick={handleSend}
            disabled={!message.trim() || disabled}
            type="button"
            title="Enviar mensagem"
          >
            <div className="child-imessage-send-icon">
              ➤
            </div>
          </button>
        </div>

        {/* Typing indicator */}
        {isTyping && (
          <div className="child-imessage-typing-indicator">
            <div className="child-imessage-typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span className="child-imessage-typing-text">A escrever...</span>
          </div>
        )}
      </div>

      {/* Emoji picker */}
      {showEmojiPicker && (
        <div className="child-imessage-emoji-picker-wrapper">
          <EmojiPicker
            onEmojiSelect={handleEmojiSelect}
            onClose={() => setShowEmojiPicker(false)}
          />
        </div>
      )}

      {/* Fun background elements */}
      <div className="child-imessage-input-bg-elements">
        <div className="child-imessage-sparkle child-imessage-sparkle-1">✨</div>
        <div className="child-imessage-sparkle child-imessage-sparkle-2">⭐</div>
        <div className="child-imessage-sparkle child-imessage-sparkle-3">💫</div>
      </div>
    </div>
  );
};

export default ChildiMessageInput;
