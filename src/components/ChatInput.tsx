import React, { useState, useRef, useEffect } from 'react';
import { useChat } from '../contexts/ChatContext';

interface ChatInputProps {
  chatId: string;
  placeholder?: string;
  disabled?: boolean;
  onSend?: (message: string) => void;
  replyTo?: {
    id: string;
    senderName: string;
    content: string;
  } | null;
  onCancelReply?: () => void;
}

/**
 * ChatInput component - Message input with typing indicators and emoji support
 */
const ChatInput: React.FC<ChatInputProps> = ({
  chatId,
  placeholder = "Escreva uma mensagem...",
  disabled = false,
  onSend,
  replyTo,
  onCancelReply
}) => {
  const { sendMessage, startTyping, stopTyping } = useChat();
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  // Handle typing indicators
  const handleTyping = () => {
    if (!isTyping) {
      setIsTyping(true);
      startTyping(chatId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Stop typing after 1 second of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      stopTyping(chatId);
    }, 1000);
  };

  // Handle message change
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    handleTyping();
  };

  // Handle send message
  const handleSend = async () => {
    const trimmedMessage = message.trim();
    if (!trimmedMessage || disabled) return;

    try {
      if (onSend) {
        onSend(trimmedMessage);
      } else {
        await sendMessage(chatId, trimmedMessage);
      }
      
      setMessage('');
      setIsTyping(false);
      stopTyping(chatId);
      
      // Clear typing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (isTyping) {
        stopTyping(chatId);
      }
    };
  }, []);

  return (
    <div className="chat-input-container">
      {/* Reply indicator */}
      {replyTo && (
        <div className="reply-indicator">
          <div className="reply-content">
            <i className="fas fa-reply me-2"></i>
            <div className="reply-details">
              <div className="reply-to">Responder a {replyTo.senderName}</div>
              <div className="reply-message">{replyTo.content}</div>
            </div>
          </div>
          <button 
            className="btn btn-sm btn-outline-secondary"
            onClick={onCancelReply}
          >
            <i className="fas fa-times"></i>
          </button>
        </div>
      )}

      {/* Input area */}
      <div className="chat-input-wrapper">
        <div className="input-group">
          {/* Emoji button */}
          <button 
            className="btn btn-outline-secondary emoji-btn"
            type="button"
            title="Emojis"
          >
            <i className="fas fa-smile"></i>
          </button>

          {/* Message textarea */}
          <textarea
            ref={textareaRef}
            className="form-control message-input"
            placeholder={placeholder}
            value={message}
            onChange={handleMessageChange}
            onKeyDown={handleKeyPress}
            disabled={disabled}
            rows={1}
            style={{ 
              resize: 'none',
              minHeight: '40px',
              maxHeight: '120px'
            }}
          />

          {/* Attachment button */}
          <button 
            className="btn btn-outline-secondary attachment-btn"
            type="button"
            title="Anexar ficheiro"
          >
            <i className="fas fa-paperclip"></i>
          </button>

          {/* Send button */}
          <button
            className="btn btn-primary send-btn"
            onClick={handleSend}
            disabled={disabled || !message.trim()}
            title="Enviar mensagem"
          >
            <i className="fas fa-paper-plane"></i>
          </button>
        </div>

        {/* Character count */}
        {message.length > 0 && (
          <div className="character-count">
            {message.length}/1000
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatInput;
