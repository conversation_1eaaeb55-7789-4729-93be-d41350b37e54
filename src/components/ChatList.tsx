import React, { useState } from 'react';
import { ChatRoom } from '../types/chat';
import { useAuth } from '../contexts/AuthContext';
import { formatDistanceToNow } from 'date-fns';
import { pt } from 'date-fns/locale';

interface ChatListProps {
  chatRooms: ChatRoom[];
  currentChatId: string | null;
  onChatSelect: (chatId: string) => void;
  onNewChat?: () => void;
  showNewChatButton?: boolean;
}

/**
 * ChatList component - List of chat rooms with search and filtering
 */
const ChatList: React.FC<ChatListProps> = ({
  chatRooms,
  currentChatId,
  onChatSelect,
  onNewChat,
  showNewChatButton = true
}) => {
  const { currentUser } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<'all' | 'family' | 'private'>('all');

  // Filter and search chat rooms
  const filteredChats = chatRooms.filter(chat => {
    const matchesSearch = chat.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      chat.lastMessage?.content.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filter === 'all' || chat.type === filter;
    
    return matchesSearch && matchesFilter;
  });

  // Get unread count for current user
  const getUnreadCount = (chat: ChatRoom) => {
    return chat.unreadCounts?.[currentUser?.uid || ''] || 0;
  };

  // Format last message time
  const formatLastMessageTime = (timestamp: any) => {
    if (!timestamp?.toDate) return '';
    
    const date = timestamp.toDate();
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString('pt-PT', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else {
      return formatDistanceToNow(date, { addSuffix: true, locale: pt });
    }
  };

  // Get chat display name
  const getChatDisplayName = (chat: ChatRoom) => {
    if (chat.type === 'family') {
      return `👨‍👩‍👧‍👦 ${chat.name}`;
    } else if (chat.type === 'private') {
      // For private chats, show the other participant's name
      const otherParticipant = chat.participantDetails.find(p => p.id !== currentUser?.uid);
      return otherParticipant ? `${otherParticipant.avatar || '👤'} ${otherParticipant.name}` : chat.name;
    }
    return chat.name;
  };

  // Get chat avatar
  const getChatAvatar = (chat: ChatRoom) => {
    if (chat.type === 'family') {
      return '👨‍👩‍👧‍👦';
    } else if (chat.type === 'private') {
      const otherParticipant = chat.participantDetails.find(p => p.id !== currentUser?.uid);
      return otherParticipant?.avatar || '👤';
    }
    return '💬';
  };

  return (
    <div className="chat-list">
      {/* Header */}
      <div className="chat-list-header">
        <div className="d-flex justify-content-between align-items-center mb-3">
          <h5 className="mb-0">
            <i className="fas fa-comments me-2"></i>
            Conversas
          </h5>
          {showNewChatButton && onNewChat && (
            <button 
              className="btn btn-primary btn-sm"
              onClick={onNewChat}
              title="Nova conversa"
            >
              <i className="fas fa-plus"></i>
            </button>
          )}
        </div>

        {/* Search */}
        <div className="search-container mb-3">
          <div className="input-group">
            <span className="input-group-text">
              <i className="fas fa-search"></i>
            </span>
            <input
              type="text"
              className="form-control"
              placeholder="Procurar conversas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Filters */}
        <div className="filter-tabs mb-3">
          <div className="btn-group w-100" role="group">
            <button
              className={`btn btn-outline-primary ${filter === 'all' ? 'active' : ''}`}
              onClick={() => setFilter('all')}
            >
              Todas
            </button>
            <button
              className={`btn btn-outline-primary ${filter === 'family' ? 'active' : ''}`}
              onClick={() => setFilter('family')}
            >
              Família
            </button>
            <button
              className={`btn btn-outline-primary ${filter === 'private' ? 'active' : ''}`}
              onClick={() => setFilter('private')}
            >
              Privadas
            </button>
          </div>
        </div>
      </div>

      {/* Chat list */}
      <div className="chat-list-content">
        {filteredChats.length === 0 ? (
          <div className="empty-state text-center py-4">
            <i className="fas fa-comments fa-3x text-muted mb-3"></i>
            <h6 className="text-muted">
              {searchTerm ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa ainda'}
            </h6>
            <p className="text-muted small">
              {searchTerm ? 'Tente procurar por outro termo' : 'Comece uma nova conversa!'}
            </p>
          </div>
        ) : (
          <div className="chat-items">
            {filteredChats.map(chat => {
              const unreadCount = getUnreadCount(chat);
              const isActive = chat.id === currentChatId;
              
              return (
                <div
                  key={chat.id}
                  className={`chat-item ${isActive ? 'active' : ''}`}
                  onClick={() => onChatSelect(chat.id)}
                >
                  <div className="chat-avatar">
                    <span className="avatar-emoji">
                      {getChatAvatar(chat)}
                    </span>
                    {/* Online indicator for private chats */}
                    {chat.type === 'private' && (
                      <div className="online-indicator"></div>
                    )}
                  </div>

                  <div className="chat-info">
                    <div className="chat-header">
                      <div className="chat-name">
                        {getChatDisplayName(chat)}
                      </div>
                      <div className="chat-time">
                        {chat.lastMessage && formatLastMessageTime(chat.lastMessage.timestamp)}
                      </div>
                    </div>

                    <div className="chat-preview">
                      <div className="last-message">
                        {chat.lastMessage ? (
                          <>
                            {chat.lastMessage.senderId === currentUser?.uid && (
                              <span className="message-sender-indicator">Você: </span>
                            )}
                            {chat.lastMessage.content}
                          </>
                        ) : (
                          <span className="no-messages">Nenhuma mensagem ainda</span>
                        )}
                      </div>
                      
                      {unreadCount > 0 && (
                        <div className="unread-badge">
                          {unreadCount > 99 ? '99+' : unreadCount}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatList;
