import React, { useState, useEffect, useRef, useCallback } from 'react';
import MessageBubble from './MessageBubble';
import MessageInput from './MessageInput';
import { Chat, ChatMessage, FamilyMember } from '../types/schema';
import { CallType, MessageType, MessageStatus, ChatType } from '../types/enums';
import { mockChatMessages } from '../data/ChatMockData';

interface ChatConversationProps {
  chat: Chat;
  currentUser: FamilyMember;
  onBack: () => void;
  onStartCall: (type: CallType) => void;
}

/**
 * ChatConversation component - Full conversation view with messages and input
 */
const ChatConversation: React.FC<ChatConversationProps> = ({
  chat,
  currentUser,
  onBack,
  onStartCall
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>(mockChatMessages);
  const [isLoading, setIsLoading] = useState(false);
  const [isPullToRefresh, setIsPullToRefresh] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const startY = useRef<number>(0);
  const isScrolling = useRef<boolean>(false);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Pull to refresh functionality
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (messagesContainerRef.current?.scrollTop === 0) {
      startY.current = e.touches[0].clientY;
      isScrolling.current = false;
    }
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (messagesContainerRef.current?.scrollTop === 0 && !isScrolling.current) {
      const currentY = e.touches[0].clientY;
      const distance = currentY - startY.current;

      if (distance > 0) {
        e.preventDefault();
        setPullDistance(Math.min(distance, 80));
        setIsPullToRefresh(distance > 60);
      }
    }
  }, []);

  const handleTouchEnd = useCallback(() => {
    if (isPullToRefresh) {
      // Simulate loading older messages
      setIsLoading(true);
      setTimeout(() => {
        setIsLoading(false);
        setIsPullToRefresh(false);
        setPullDistance(0);
      }, 1000);
    } else {
      setPullDistance(0);
      setIsPullToRefresh(false);
    }
    isScrolling.current = false;
  }, [isPullToRefresh]);

  const handleScroll = useCallback(() => {
    isScrolling.current = true;
  }, []);

  const getOtherParticipant = (): FamilyMember | null => {
    if (chat.type === ChatType.PRIVATE) {
      return chat.participants.find(p => p.id !== currentUser.id) || null;
    }
    return null;
  };

  const getChatTitle = (): string => {
    if (chat.type === ChatType.FAMILY) {
      return chat.name;
    }
    const otherParticipant = getOtherParticipant();
    return otherParticipant?.name || chat.name;
  };

  const getChatSubtitle = (): string => {
    if (chat.type === ChatType.FAMILY) {
      return `${chat.participants.length} membros`;
    }
    const otherParticipant = getOtherParticipant();
    if (otherParticipant) {
      switch (otherParticipant.onlineStatus) {
        case 'online':
          return 'Online';
        case 'away':
          return 'Ausente';
        case 'offline':
          return 'Offline';
        default:
          return 'Offline';
      }
    }
    return '';
  };

  const handleSendMessage = (content: string) => {
    const newMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      senderId: currentUser.id,
      senderName: currentUser.name,
      content,
      type: MessageType.TEXT,
      timestamp: new Date(),
      status: MessageStatus.SENDING
    };

    setMessages(prev => [...prev, newMessage]);

    // Simulate message sending
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: MessageStatus.SENT }
            : msg
        )
      );
    }, 1000);

    // Simulate message delivery
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: MessageStatus.DELIVERED }
            : msg
        )
      );
    }, 2000);
  };

  return (
    <div className="chat-conversation d-flex flex-column">
      {/* Conversation Header */}
      <div className="conversation-header">
        <div className="d-flex align-items-center">
          <button
            className="btn btn-link text-white p-0 me-3 mobile-touch-target"
            onClick={onBack}
            title="Voltar à lista de chats"
            style={{ minWidth: '44px', minHeight: '44px' }}
          >
            <i className="fas fa-arrow-left fa-lg"></i>
          </button>

          <div className="flex-grow-1">
            <h5 className="mb-0 text-white fw-bold">{getChatTitle()}</h5>
            <small className="text-white-50">{getChatSubtitle()}</small>
          </div>

          <div className="btn-group">
            <button
              className="btn btn-outline-light btn-sm mobile-touch-target"
              onClick={() => onStartCall(CallType.AUDIO)}
              title="Chamada de áudio"
              style={{ minWidth: '44px', minHeight: '44px' }}
            >
              <i className="fas fa-phone"></i>
            </button>
            
            <button
              className="btn btn-outline-light btn-sm"
              onClick={() => onStartCall(CallType.VIDEO)}
              title="Chamada de vídeo"
            >
              <i className="fas fa-video"></i>
            </button>
            
            <div className="dropdown">
              <button
                className="btn btn-outline-light btn-sm dropdown-toggle"
                type="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
                title="Mais opções"
              >
                <i className="fas fa-ellipsis-v"></i>
              </button>
              <ul className="dropdown-menu dropdown-menu-end">
                <li>
                  <button className="dropdown-item" type="button">
                    <i className="fas fa-info-circle me-2"></i>
                    Detalhes do chat
                  </button>
                </li>
                <li>
                  <button className="dropdown-item" type="button">
                    <i className="fas fa-volume-mute me-2"></i>
                    Silenciar notificações
                  </button>
                </li>
                <li><hr className="dropdown-divider" /></li>
                <li>
                  <button className="dropdown-item text-danger" type="button">
                    <i className="fas fa-trash me-2"></i>
                    Limpar histórico
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div
        ref={messagesContainerRef}
        className="conversation-messages flex-grow-1 position-relative"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onScroll={handleScroll}
        style={{
          transform: `translateY(${pullDistance * 0.5}px)`,
          transition: isPullToRefresh ? 'none' : 'transform 0.3s ease'
        }}
      >
        {/* Pull to refresh indicator */}
        <div
          className={`pull-to-refresh ${isPullToRefresh ? 'visible' : ''}`}
          style={{
            opacity: pullDistance > 0 ? Math.min(pullDistance / 60, 1) : 0,
            transform: `translateY(${Math.max(0, pullDistance - 60)}px)`
          }}
        >
          <i className={`fas ${isPullToRefresh ? 'fa-sync-alt fa-spin' : 'fa-arrow-down'}`}></i>
        </div>

        {isLoading && (
          <div className="text-center py-3">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">A carregar mensagens...</span>
            </div>
          </div>
        )}

        {messages.map((message, index) => {
          const showAvatar = index === 0 ||
            messages[index - 1].senderId !== message.senderId;

          return (
            <MessageBubble
              key={message.id}
              message={message}
              currentUser={currentUser}
              showAvatar={showAvatar}
            />
          );
        })}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input - Fixed above mobile navbar */}
      <div className="message-input-fixed">
        <MessageInput
          onSendMessage={handleSendMessage}
          placeholder={`Mensagem para ${getChatTitle()}...`}
        />
      </div>
    </div>
  );
};

export default ChatConversation;