import React, { useState, useRef } from 'react';
import { Chat, FamilyMember } from '../types/schema';
import { ChatType, OnlineStatus } from '../types/enums';
import { formatMessageTime, formatUnreadCount } from '../utils/formatters';
import { swipeHaptic, buttonPressHaptic } from '../utils/hapticFeedback';

interface ChatItemProps {
  chat: Chat;
  currentUser: FamilyMember;
  onChatSelect: (chatId: string) => void;
  onSwipeAction: (chatId: string, action: string) => void;
}

/**
 * ChatItem component - Individual chat item with swipe actions
 */
const ChatItem: React.FC<ChatItemProps> = ({ 
  chat, 
  currentUser, 
  onChatSelect, 
  onSwipeAction 
}) => {
  const [isSwiped, setIsSwiped] = useState(false);
  const [startX, setStartX] = useState(0);
  const itemRef = useRef<HTMLDivElement>(null);

  const getOtherParticipant = (): FamilyMember | null => {
    if (chat.type === ChatType.PRIVATE) {
      return chat.participants.find(p => p.id !== currentUser.id) || null;
    }
    return null;
  };

  const getDisplayName = (): string => {
    if (chat.type === ChatType.FAMILY) {
      return chat.name;
    }
    const otherParticipant = getOtherParticipant();
    return otherParticipant?.name || chat.name;
  };

  const getDisplayAvatar = (): string => {
    if (chat.type === ChatType.FAMILY) {
      return '👨‍👩‍👧‍👦';
    }
    const otherParticipant = getOtherParticipant();
    return otherParticipant?.avatar || '💬';
  };

  const getOnlineStatus = (): OnlineStatus | null => {
    if (chat.type === ChatType.PRIVATE) {
      const otherParticipant = getOtherParticipant();
      return otherParticipant?.onlineStatus || null;
    }
    return null;
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    setStartX(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!startX) return;

    const currentX = e.touches[0].clientX;
    const diffX = startX - currentX;

    // More sensitive swipe detection for better UX
    if (diffX > 30 && !isSwiped) {
      setIsSwiped(true);
      swipeHaptic();
    } else if (diffX < -30 && isSwiped) {
      setIsSwiped(false);
    }
  };

  const handleTouchEnd = () => {
    setStartX(0);
  };

  const handleSwipeAction = (action: string) => {
    onSwipeAction(chat.id, action);
    setIsSwiped(false);
  };

  const handleClick = () => {
    if (!isSwiped) {
      buttonPressHaptic();
      onChatSelect(chat.id);
    }
  };

  const getChatItemClass = (): string => {
    let className = 'list-group-item chat-item';
    
    if (chat.type === ChatType.FAMILY) {
      className += ' family-chat';
    } else {
      className += ' private-chat';
    }
    
    if (chat.unreadCount > 0) {
      className += ' unread';
    }
    
    if (chat.status === 'archived') {
      className += ' archived';
    }
    
    if (isSwiped) {
      className += ' swiped';
    }
    
    return className;
  };

  const onlineStatus = getOnlineStatus();

  return (
    <div
      ref={itemRef}
      className={`${getChatItemClass()} mobile-touch-target`}
      onClick={handleClick}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{
        minHeight: '72px',
        transition: 'all 0.3s ease',
        transform: isSwiped ? 'translateX(-80px)' : 'translateX(0)'
      }}
    >
      <div className="d-flex align-items-center">
        <div className="chat-avatar me-3">
          {getDisplayAvatar()}
          {onlineStatus && (
            <div className={`online-status ${onlineStatus}`}></div>
          )}
        </div>
        
        <div className="chat-info flex-grow-1">
          <div className="d-flex justify-content-between align-items-start">
            <div className="flex-grow-1 min-width-0">
              <div className="chat-name">
                {getDisplayName()}
                {chat.isPinned && (
                  <i className="fas fa-thumbtack ms-2 text-warning"></i>
                )}
              </div>
              {chat.lastMessage && (
                <div className="chat-last-message">
                  {chat.lastMessage.senderName === currentUser.name ? 'Você: ' : ''}
                  {chat.lastMessage.content}
                </div>
              )}
            </div>
            
            <div className="chat-meta">
              {chat.lastMessage && (
                <div className="chat-time">
                  {formatMessageTime(chat.lastMessage.timestamp)}
                </div>
              )}
              {chat.unreadCount > 0 && (
                <span className="chat-unread-badge">
                  {formatUnreadCount(chat.unreadCount)}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Swipe Actions */}
      <div className="swipe-actions">
        <button
          className="swipe-action archive"
          onClick={(e) => {
            e.stopPropagation();
            handleSwipeAction(chat.status === 'archived' ? 'unarchive' : 'archive');
          }}
          title={chat.status === 'archived' ? 'Desarquivar' : 'Arquivar'}
        >
          <i className="fas fa-archive"></i>
        </button>
        
        <button
          className="swipe-action read"
          onClick={(e) => {
            e.stopPropagation();
            handleSwipeAction(chat.unreadCount > 0 ? 'mark-read' : 'mark-unread');
          }}
          title={chat.unreadCount > 0 ? 'Marcar como lida' : 'Marcar como não lida'}
        >
          <i className={`fas ${chat.unreadCount > 0 ? 'fa-check' : 'fa-envelope'}`}></i>
        </button>
        
        <button
          className="swipe-action mute"
          onClick={(e) => {
            e.stopPropagation();
            handleSwipeAction(chat.status === 'muted' ? 'unmute' : 'mute');
          }}
          title={chat.status === 'muted' ? 'Ativar som' : 'Silenciar'}
        >
          <i className={`fas ${chat.status === 'muted' ? 'fa-volume-up' : 'fa-volume-mute'}`}></i>
        </button>
        
        {chat.type === ChatType.PRIVATE && (
          <button
            className="swipe-action delete"
            onClick={(e) => {
              e.stopPropagation();
              handleSwipeAction('delete');
            }}
            title="Eliminar"
          >
            <i className="fas fa-trash"></i>
          </button>
        )}
      </div>
    </div>
  );
};

export default ChatItem;