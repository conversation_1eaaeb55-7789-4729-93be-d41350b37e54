import React, { useState, useRef, useEffect } from 'react';
import EmojiPicker from './EmojiPicker';
import { useVirtualKeyboard } from '../hooks/useVirtualKeyboard';
import { buttonPressHaptic, successHaptic } from '../utils/hapticFeedback';

interface MessageInputProps {
  onSendMessage: (content: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

/**
 * MessageInput component - Input area for typing and sending messages
 */
const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = "Escrever mensagem..."
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { isKeyboardOpen } = useVirtualKeyboard();

  // Handle virtual keyboard on mobile
  useEffect(() => {
    if (isKeyboardOpen) {
      containerRef.current?.classList.add('keyboard-open');
    } else {
      containerRef.current?.classList.remove('keyboard-open');
    }
  }, [isKeyboardOpen]);

  useEffect(() => {
    const handleFocus = () => {
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 300);
    };

    textareaRef.current?.addEventListener('focus', handleFocus);

    return () => {
      textareaRef.current?.removeEventListener('focus', handleFocus);
    };
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (message.trim() && !disabled) {
      successHaptic();
      onSendMessage(message.trim());
      setMessage('');
      setIsTyping(false);

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);
    setIsTyping(value.length > 0);
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    const textarea = textareaRef.current;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newMessage = message.slice(0, start) + emoji + message.slice(end);
      setMessage(newMessage);
      setIsTyping(newMessage.length > 0);
      
      // Focus back to textarea and set cursor position
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + emoji.length, start + emoji.length);
      }, 0);
    }
  };

  const toggleEmojiPicker = () => {
    buttonPressHaptic();
    setShowEmojiPicker(!showEmojiPicker);
  };

  return (
    <div ref={containerRef} className="message-input-container">
      <form onSubmit={handleSubmit} className="d-flex align-items-end gap-2">
        <div className="flex-grow-1">
          <div className="input-group">
            <button
              type="button"
              className="btn btn-outline-secondary mobile-touch-target"
              title="Anexar ficheiro"
              disabled={disabled}
              style={{ minWidth: '48px' }}
            >
              <i className="fas fa-paperclip"></i>
            </button>

            <textarea
              ref={textareaRef}
              className="form-control message-input"
              value={message}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder={placeholder}
              disabled={disabled}
              rows={1}
              style={{
                fontSize: window.innerWidth <= 768 ? '16px' : '14px',
                minHeight: '44px'
              }}
            />

            <button
              type="button"
              className="btn btn-outline-secondary mobile-touch-target"
              title="Emoji"
              disabled={disabled}
              onClick={toggleEmojiPicker}
              style={{ minWidth: '48px' }}
            >
              <i className="fas fa-smile"></i>
            </button>
          </div>
        </div>

        <button
          type="submit"
          className={`btn btn-primary btn-send-mobile ${message.trim() ? 'btn-send-active' : ''}`}
          disabled={!message.trim() || disabled}
          title="Enviar mensagem"
        >
          <i className="fas fa-paper-plane"></i>
        </button>
      </form>
      
      {isTyping && (
        <div className="typing-indicator mt-1">
          <small className="text-muted">
            <i className="fas fa-keyboard me-1"></i>
            A escrever...
          </small>
        </div>
      )}

      {/* Emoji Picker */}
      <EmojiPicker
        isVisible={showEmojiPicker}
        onEmojiSelect={handleEmojiSelect}
        onClose={() => setShowEmojiPicker(false)}
      />
    </div>
  );
};

export default MessageInput;