import React from 'react';
import Logo from './Logo';
import { DashboardUser } from '../types/schema';

interface DashboardHeaderProps {
  user: DashboardUser;
  onLogout?: () => void;
}

/**
 * DashboardHeader component - Header section with logo and user info
 * Features Nintendo-style gaming design with consistent branding
 */
const DashboardHeader: React.FC<DashboardHeaderProps> = ({ user, onLogout }) => {
  return (
    <div className="dashboard-header">
      <div className="container">
        <div className="row align-items-center">
          <div className="col-md-6">
            <div className="d-flex align-items-center">
              <Logo size="sm" className="me-3" />
              <div className="text-white">
                <h4 className="mb-0 fw-bold">Dashboard dos Pais</h4>
                <small className="opacity-75">Bem-vindo de volta, {user.name}!</small>
              </div>
            </div>
          </div>
          
          <div className="col-md-6 text-md-end mt-3 mt-md-0">
            <div className="d-flex align-items-center justify-content-md-end">
              <div className="text-white me-3">
                <div className="fw-semibold">{user.email}</div>
                <small className="opacity-75">Conta de Adulto</small>
              </div>
              {onLogout && (
                <button
                  className="btn btn-outline-light btn-sm"
                  onClick={onLogout}
                  title="Terminar sessão"
                >
                  <i className="fas fa-sign-out-alt me-2"></i>
                  Sair
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardHeader;