import React from 'react';
import { ChatMessage, FamilyMember } from '../types/schema';
import { MessageStatus, ChatType } from '../types/enums';
import { formatMessageTime } from '../utils/formatters';

interface ChildiMessageBubbleProps {
  message: ChatMessage;
  currentUser: FamilyMember;
  showAvatar?: boolean;
  chatType?: ChatType;
}

/**
 * ChildiMessageBubble component - iMessage-style message bubble for children
 * Features colorful, rounded bubbles with child-friendly design
 */
const ChildiMessageBubble: React.FC<ChildiMessageBubbleProps> = ({ 
  message, 
  currentUser, 
  showAvatar = true,
  chatType = ChatType.FAMILY
}) => {
  const isOwnMessage = message.senderId === currentUser.id;

  const getStatusIcon = (status: MessageStatus): string => {
    switch (status) {
      case MessageStatus.SENDING:
        return '⏳';
      case MessageStatus.SENT:
        return '✓';
      case MessageStatus.DELIVERED:
        return '✓✓';
      case MessageStatus.READ:
        return '👀';
      case MessageStatus.FAILED:
        return '❌';
      default:
        return '✓';
    }
  };

  const getSenderEmoji = (senderName: string): string => {
    // Map sender names to emojis for fun visual representation
    const emojiMap: { [key: string]: string } = {
      'Mãe': '👩',
      'Pai': '👨',
      'João': '👦',
      'Maria': '👧',
      'Ana': '👧',
      'Pedro': '👦',
      'Sofia': '👧',
      'Miguel': '👦'
    };
    return emojiMap[senderName] || '😊';
  };

  const getBubbleGradient = (isOwn: boolean): string => {
    if (isOwn) {
      // Child's messages - purple gradient like in image
      return 'linear-gradient(135deg, #7c3aed, #a855f7)';
    } else {
      // Received messages - white background
      return 'white';
    }
  };

  return (
    <div className="child-imessage-bubble-container">
      <div className={`child-imessage-bubble-wrapper ${isOwnMessage ? 'own-message' : 'received-message'}`}>
        {/* Avatar for received messages */}
        {!isOwnMessage && showAvatar && (
          <div className="child-imessage-avatar">
            <div className="child-imessage-avatar-emoji">
              {getSenderEmoji(message.senderName)}
            </div>
          </div>
        )}
        
        <div className="child-imessage-bubble-group">
          {/* Sender name for group chats */}
          {!isOwnMessage && chatType === ChatType.FAMILY && (
            <div className="child-imessage-sender-name">
              {message.senderName}
            </div>
          )}
          
          {/* Message bubble */}
          <div 
            className={`child-imessage-bubble ${isOwnMessage ? 'sent' : 'received'}`}
            style={{
              background: getBubbleGradient(isOwnMessage)
            }}
          >
            <div className="child-imessage-content">
              {message.content}
            </div>
            
            {/* Message tail */}
            <div className={`child-imessage-tail ${isOwnMessage ? 'sent-tail' : 'received-tail'}`}></div>
          </div>
          
          {/* Message info */}
          <div className={`child-imessage-info ${isOwnMessage ? 'sent' : 'received'}`}>
            <span className="child-imessage-time">
              {formatMessageTime(message.timestamp)}
            </span>

            {isOwnMessage && (
              <span className="child-imessage-status ms-2">
                {getStatusIcon(message.status)}
              </span>
            )}
          </div>
        </div>
        
        {/* Avatar for sent messages */}
        {isOwnMessage && showAvatar && (
          <div className="child-imessage-avatar">
            <div className="child-imessage-avatar-emoji">
              {getSenderEmoji(currentUser.name)}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChildiMessageBubble;
