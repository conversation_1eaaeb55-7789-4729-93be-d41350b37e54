import React, { useEffect, useRef, useState } from 'react';
import { ChatRoom, ChatMessage as ChatMessageType, TypingIndicator } from '../types/chat';
import { useAuth } from '../contexts/AuthContext';
import { useChat } from '../contexts/ChatContext';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';

interface ChatWindowProps {
  chatRoom: ChatRoom | null;
  messages: ChatMessageType[];
  typingUsers: TypingIndicator[];
  onBack?: () => void;
  showBackButton?: boolean;
}

/**
 * ChatWindow component - Main chat interface with messages and input
 */
const ChatWindow: React.FC<ChatWindowProps> = ({
  chatRoom,
  messages,
  typingUsers,
  onBack,
  showBackButton = false
}) => {
  const { currentUser } = useAuth();
  const { editMessage, deleteMessage, markAsRead } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [replyTo, setReplyTo] = useState<{
    id: string;
    senderName: string;
    content: string;
  } | null>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Mark messages as read when chat opens
  useEffect(() => {
    if (chatRoom && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.senderId !== currentUser?.uid && lastMessage.status !== 'read') {
        markAsRead(chatRoom.id, lastMessage.id);
      }
    }
  }, [chatRoom, messages, currentUser, markAsRead]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleReply = (message: ChatMessageType) => {
    setReplyTo({
      id: message.id,
      senderName: message.senderName,
      content: message.content
    });
  };

  const handleCancelReply = () => {
    setReplyTo(null);
  };

  // Get chat display info
  const getChatDisplayInfo = () => {
    if (!chatRoom) return { name: '', subtitle: '' };

    if (chatRoom.type === 'family') {
      return {
        name: `👨‍👩‍👧‍👦 ${chatRoom.name}`,
        subtitle: `${chatRoom.participants.length} membros`
      };
    } else if (chatRoom.type === 'private') {
      const otherParticipant = chatRoom.participantDetails.find(p => p.id !== currentUser?.uid);
      return {
        name: otherParticipant ? `${otherParticipant.avatar || '👤'} ${otherParticipant.name}` : chatRoom.name,
        subtitle: otherParticipant?.isOnline ? 'Online' : 'Offline'
      };
    }

    return {
      name: chatRoom.name,
      subtitle: `${chatRoom.participants.length} participantes`
    };
  };

  const { name: chatName, subtitle: chatSubtitle } = getChatDisplayInfo();

  if (!chatRoom) {
    return (
      <div className="chat-window-empty">
        <div className="empty-state text-center">
          <i className="fas fa-comments fa-4x text-muted mb-3"></i>
          <h4 className="text-muted">Selecione uma conversa</h4>
          <p className="text-muted">
            Escolha uma conversa da lista para começar a enviar mensagens
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="chat-window">
      {/* Chat header */}
      <div className="chat-header">
        <div className="d-flex align-items-center">
          {showBackButton && (
            <button 
              className="btn btn-link text-white me-2 p-0"
              onClick={onBack}
            >
              <i className="fas fa-arrow-left"></i>
            </button>
          )}
          
          <div className="chat-info">
            <h6 className="chat-title mb-0">{chatName}</h6>
            <small className="chat-subtitle">{chatSubtitle}</small>
          </div>
        </div>

        <div className="chat-actions">
          <button className="btn btn-link text-white" title="Informações do chat">
            <i className="fas fa-info-circle"></i>
          </button>
          <button className="btn btn-link text-white" title="Chamada de vídeo">
            <i className="fas fa-video"></i>
          </button>
          <button className="btn btn-link text-white" title="Mais opções">
            <i className="fas fa-ellipsis-v"></i>
          </button>
        </div>
      </div>

      {/* Messages area */}
      <div className="chat-messages">
        <div className="messages-container">
          {messages.length === 0 ? (
            <div className="no-messages text-center py-4">
              <i className="fas fa-comment-dots fa-2x text-muted mb-2"></i>
              <p className="text-muted">Nenhuma mensagem ainda</p>
              <p className="text-muted small">Seja o primeiro a enviar uma mensagem!</p>
            </div>
          ) : (
            <>
              {messages.map((message, index) => {
                // Helper function to get date from timestamp
                const getDateFromTimestamp = (timestamp: any) => {
                  if (!timestamp) return null;
                  if (timestamp.toDate) return timestamp.toDate();
                  if (timestamp instanceof Date) return timestamp;
                  if (timestamp.seconds) return new Date(timestamp.seconds * 1000);
                  return null;
                };

                const currentDate = getDateFromTimestamp(message.timestamp);
                const previousDate = index > 0 ? getDateFromTimestamp(messages[index - 1].timestamp) : null;

                const showDateSeparator = index === 0 ||
                  (currentDate && previousDate && currentDate.toDateString() !== previousDate.toDateString());

                return (
                  <React.Fragment key={message.id}>
                    {showDateSeparator && currentDate && (
                      <div className="date-separator">
                        <span className="date-text">
                          {currentDate.toLocaleDateString('pt-PT', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </span>
                      </div>
                    )}

                    <ChatMessage
                      message={message}
                      onEdit={editMessage}
                      onDelete={deleteMessage}
                      onReply={handleReply}
                    />
                  </React.Fragment>
                );
              })}
            </>
          )}

          {/* Typing indicators */}
          {typingUsers.length > 0 && (
            <div className="typing-indicators">
              <div className="typing-bubble">
                <div className="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <div className="typing-text">
                  {typingUsers.length === 1 
                    ? `${typingUsers[0].userName} está a escrever...`
                    : `${typingUsers.length} pessoas estão a escrever...`
                  }
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Chat input */}
      <div className="chat-input-area">
        <ChatInput
          chatId={chatRoom.id}
          replyTo={replyTo}
          onCancelReply={handleCancelReply}
        />
      </div>
    </div>
  );
};

export default ChatWindow;
