import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useChat } from '../contexts/ChatContext';

/**
 * SimpleChatTest component - Basic chat test to debug issues
 */
const SimpleChatTest: React.FC = () => {
  const { currentUser, userProfile } = useAuth();
  const { chatRooms, messages, sendMessage, createChatRoom } = useChat();
  const [newMessage, setNewMessage] = useState('');
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [error, setError] = useState<string>('');

  // Auto-select first chat or create one
  useEffect(() => {
    if (chatRooms.length > 0 && !selectedChatId) {
      setSelectedChatId(chatRooms[0].id);
    } else if (chatRooms.length === 0 && currentUser) {
      // Create a test chat
      createChatRoom('Chat de Teste', 'family', [])
        .then(chatId => setSelectedChatId(chatId))
        .catch(err => setError('Erro ao criar chat: ' + err.message));
    }
  }, [chatRooms, selectedChatId, currentUser, createChatRoom]);

  const handleSendMessage = async () => {
    if (!selectedChatId || !newMessage.trim()) return;

    try {
      setError('');
      await sendMessage(selectedChatId, newMessage.trim());
      setNewMessage('');
    } catch (err: any) {
      setError('Erro ao enviar mensagem: ' + err.message);
      console.error('Send message error:', err);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!currentUser) {
    return <div>Por favor, faça login primeiro.</div>;
  }

  const currentMessages = selectedChatId ? messages[selectedChatId] || [] : [];

  return (
    <div className="simple-chat-test" style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h3>Chat Test - Debug</h3>
      
      {/* User Info */}
      <div style={{ background: '#f8f9fa', padding: '10px', borderRadius: '8px', marginBottom: '20px' }}>
        <strong>Utilizador:</strong> {userProfile?.displayName || 'Carregando...'} ({userProfile?.role})
        <br />
        <strong>UID:</strong> {currentUser.uid}
      </div>

      {/* Error Display */}
      {error && (
        <div style={{ background: '#f8d7da', color: '#721c24', padding: '10px', borderRadius: '8px', marginBottom: '20px' }}>
          {error}
        </div>
      )}

      {/* Chat Rooms */}
      <div style={{ marginBottom: '20px' }}>
        <strong>Chats Disponíveis ({chatRooms.length}):</strong>
        {chatRooms.length === 0 ? (
          <div>Nenhum chat encontrado. Criando chat de teste...</div>
        ) : (
          <div>
            {chatRooms.map(room => (
              <div 
                key={room.id}
                style={{ 
                  padding: '10px', 
                  border: selectedChatId === room.id ? '2px solid blue' : '1px solid #ccc',
                  borderRadius: '8px',
                  margin: '5px 0',
                  cursor: 'pointer'
                }}
                onClick={() => setSelectedChatId(room.id)}
              >
                <strong>{room.name}</strong> ({room.participants.length} participantes)
                <br />
                <small>ID: {room.id}</small>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Messages */}
      {selectedChatId && (
        <div>
          <strong>Mensagens ({currentMessages.length}):</strong>
          <div style={{ 
            height: '300px', 
            border: '1px solid #ccc', 
            borderRadius: '8px', 
            padding: '10px', 
            overflowY: 'auto',
            background: 'white',
            marginBottom: '10px'
          }}>
            {currentMessages.length === 0 ? (
              <div style={{ color: '#666', fontStyle: 'italic' }}>
                Nenhuma mensagem ainda. Seja o primeiro a enviar!
              </div>
            ) : (
              currentMessages.map(message => (
                <div 
                  key={message.id}
                  style={{ 
                    marginBottom: '10px',
                    padding: '8px',
                    background: message.senderId === currentUser.uid ? '#e3f2fd' : '#f5f5f5',
                    borderRadius: '8px',
                    textAlign: message.senderId === currentUser.uid ? 'right' : 'left'
                  }}
                >
                  <div style={{ fontWeight: 'bold', fontSize: '0.9em' }}>
                    {message.senderName} {message.senderAvatar}
                  </div>
                  <div>{message.content}</div>
                  <div style={{ fontSize: '0.8em', color: '#666', marginTop: '4px' }}>
                    {message.timestamp?.toDate ? 
                      message.timestamp.toDate().toLocaleTimeString() : 
                      'Enviando...'
                    }
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Message Input */}
          <div style={{ display: 'flex', gap: '10px' }}>
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Digite sua mensagem..."
              style={{ 
                flex: 1, 
                padding: '10px', 
                border: '1px solid #ccc', 
                borderRadius: '8px' 
              }}
            />
            <button
              onClick={handleSendMessage}
              disabled={!newMessage.trim()}
              style={{ 
                padding: '10px 20px', 
                background: '#007bff', 
                color: 'white', 
                border: 'none', 
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              Enviar
            </button>
          </div>
        </div>
      )}

      {/* Debug Info */}
      <details style={{ marginTop: '20px' }}>
        <summary>Debug Info</summary>
        <pre style={{ background: '#f8f9fa', padding: '10px', borderRadius: '8px', fontSize: '12px' }}>
          {JSON.stringify({
            chatRoomsCount: chatRooms.length,
            selectedChatId,
            messagesCount: currentMessages.length,
            userProfile: userProfile ? {
              displayName: userProfile.displayName,
              role: userProfile.role,
              uid: userProfile.uid
            } : null
          }, null, 2)}
        </pre>
      </details>
    </div>
  );
};

export default SimpleChatTest;
