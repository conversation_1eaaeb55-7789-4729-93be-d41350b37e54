import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireRole?: 'adult' | 'child';
  redirectTo?: string;
}

/**
 * ProtectedRoute component - Protects routes based on authentication and role
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requireRole,
  redirectTo = '/login'
}) => {
  const { currentUser, userProfile, loading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking auth state
  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '100vh' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  // Check if authentication is required
  if (requireAuth && !currentUser) {
    // Redirect to login page with return URL
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Check if specific role is required
  if (requireRole && userProfile?.role !== requireRole) {
    // Redirect based on user role
    if (userProfile?.role === 'adult') {
      return <Navigate to="/adult-dashboard" replace />;
    } else if (userProfile?.role === 'child') {
      return <Navigate to="/child-dashboard" replace />;
    } else {
      return <Navigate to="/login" replace />;
    }
  }

  // If user is authenticated but trying to access login/register pages
  if (!requireAuth && currentUser) {
    // Redirect authenticated users away from login/register pages
    if (userProfile?.role === 'adult') {
      return <Navigate to="/adult-dashboard" replace />;
    } else if (userProfile?.role === 'child') {
      return <Navigate to="/child-dashboard" replace />;
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;
