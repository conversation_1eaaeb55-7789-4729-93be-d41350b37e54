import React, { useState } from 'react';
import { ChatMessage as ChatMessageType } from '../types/chat';
import { useAuth } from '../contexts/AuthContext';
import { formatDistanceToNow } from 'date-fns';
import { pt } from 'date-fns/locale';

interface ChatMessageProps {
  message: ChatMessageType;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onReply?: (message: ChatMessageType) => void;
  showActions?: boolean;
}

/**
 * ChatMessage component - Individual chat message with actions
 */
const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  onEdit,
  onDelete,
  onReply,
  showActions = true
}) => {
  const { currentUser } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);
  const [showMenu, setShowMenu] = useState(false);

  const isOwnMessage = currentUser?.uid === message.senderId;

  // Handle different timestamp formats
  const getMessageTime = () => {
    if (!message.timestamp) return 'agora';

    let date: Date;
    if (message.timestamp.toDate) {
      // Firestore Timestamp
      date = message.timestamp.toDate();
    } else if (message.timestamp instanceof Date) {
      // Regular Date
      date = message.timestamp;
    } else if (typeof message.timestamp === 'object' && message.timestamp.seconds) {
      // Firestore Timestamp object
      date = new Date(message.timestamp.seconds * 1000);
    } else {
      return 'agora';
    }

    try {
      return formatDistanceToNow(date, { addSuffix: true, locale: pt });
    } catch (error) {
      return date.toLocaleTimeString();
    }
  };

  const messageTime = getMessageTime();

  const handleEdit = () => {
    if (onEdit && editContent.trim() !== message.content) {
      onEdit(message.id, editContent.trim());
    }
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleEdit();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditContent(message.content);
    }
  };

  return (
    <div className={`chat-message ${isOwnMessage ? 'own-message' : 'other-message'}`}>
      <div className="message-container">
        {/* Avatar for other users */}
        {!isOwnMessage && (
          <div className="message-avatar">
            <span className="avatar-emoji">{message.senderAvatar || '👤'}</span>
          </div>
        )}

        <div className="message-content">
          {/* Sender name for other users */}
          {!isOwnMessage && (
            <div className="message-sender">
              {message.senderName}
            </div>
          )}

          {/* Message bubble */}
          <div className={`message-bubble ${isOwnMessage ? 'own-bubble' : 'other-bubble'}`}>
            {isEditing ? (
              <div className="message-edit">
                <textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  onKeyDown={handleKeyPress}
                  onBlur={handleEdit}
                  className="edit-textarea"
                  autoFocus
                />
                <div className="edit-actions">
                  <button 
                    className="btn btn-sm btn-success"
                    onClick={handleEdit}
                  >
                    <i className="fas fa-check"></i>
                  </button>
                  <button 
                    className="btn btn-sm btn-secondary"
                    onClick={() => {
                      setIsEditing(false);
                      setEditContent(message.content);
                    }}
                  >
                    <i className="fas fa-times"></i>
                  </button>
                </div>
              </div>
            ) : (
              <>
                <div className="message-text">
                  {message.content}
                  {message.edited && (
                    <span className="edited-indicator"> (editado)</span>
                  )}
                </div>

                {/* Message actions */}
                {showActions && (
                  <div className="message-actions">
                    <button
                      className="action-btn"
                      onClick={() => setShowMenu(!showMenu)}
                    >
                      <i className="fas fa-ellipsis-h"></i>
                    </button>

                    {showMenu && (
                      <div className="message-menu">
                        {onReply && (
                          <button
                            className="menu-item"
                            onClick={() => {
                              onReply(message);
                              setShowMenu(false);
                            }}
                          >
                            <i className="fas fa-reply me-2"></i>
                            Responder
                          </button>
                        )}
                        
                        {isOwnMessage && onEdit && (
                          <button
                            className="menu-item"
                            onClick={() => {
                              setIsEditing(true);
                              setShowMenu(false);
                            }}
                          >
                            <i className="fas fa-edit me-2"></i>
                            Editar
                          </button>
                        )}
                        
                        {isOwnMessage && onDelete && (
                          <button
                            className="menu-item delete"
                            onClick={() => {
                              if (window.confirm('Tem certeza que deseja apagar esta mensagem?')) {
                                onDelete(message.id);
                              }
                              setShowMenu(false);
                            }}
                          >
                            <i className="fas fa-trash me-2"></i>
                            Apagar
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>

          {/* Message metadata */}
          <div className="message-meta">
            <span className="message-time">{messageTime}</span>
            {isOwnMessage && (
              <span className={`message-status ${message.status}`}>
                {message.status === 'sent' && <i className="fas fa-check"></i>}
                {message.status === 'delivered' && <i className="fas fa-check-double"></i>}
                {message.status === 'read' && <i className="fas fa-check-double text-primary"></i>}
              </span>
            )}
          </div>
        </div>

        {/* Avatar for own messages */}
        {isOwnMessage && (
          <div className="message-avatar">
            <span className="avatar-emoji">{message.senderAvatar || '👤'}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
