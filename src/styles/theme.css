/* SuperTarefa Nintendo-style Gaming Theme */
/* Colors extracted from the logo */

:root {
  /* Logo Colors */
  --st-red: #ff4444;
  --st-orange: #ff6b35;
  --st-yellow: #ffd700;
  --st-blue: #4a90e2;
  --st-green: #4caf50;
  
  /* Gaming Theme Colors */
  --st-bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --st-bg-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --st-shadow-game: 0 8px 32px rgba(0, 0, 0, 0.3);
  --st-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.4);
  
  /* Nintendo-style borders */
  --st-border-radius: 20px;
  --st-border-radius-lg: 25px;
  
  /* Typography */
  --st-font-game: 'Fredoka One', 'Comic Sans MS', cursive;
  --st-font-primary: 'Poppins', system-ui, sans-serif;
}

/* Gaming Background */
.login-page {
  background: var(--st-bg-primary);
  position: relative;
  overflow: hidden;
}

.login-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(76, 175, 80, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 68, 68, 0.1) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Logo Styles */
.logo-sm { max-width: 120px; }
.logo-md { max-width: 180px; }
.logo-lg { max-width: 250px; }

.logo-bounce {
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* Game Title */
.game-title {
  font-family: var(--st-font-game);
  text-shadow: 3px 3px 0px rgba(0, 0, 0, 0.3);
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { text-shadow: 3px 3px 0px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 215, 0, 0.3); }
  to { text-shadow: 3px 3px 0px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 215, 0, 0.6); }
}

/* Login Cards */
.login-card-child {
  background: linear-gradient(145deg, #fff 0%, #f8f9ff 100%);
  border: 4px solid var(--st-orange);
  border-radius: var(--st-border-radius-lg);
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-card-child:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--st-shadow-hover);
  border-color: var(--st-yellow);
}

.login-card-adult {
  background: linear-gradient(145deg, #fff 0%, #f0f8ff 100%);
  border: 4px solid var(--st-blue);
  border-radius: var(--st-border-radius-lg);
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-card-adult:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--st-shadow-hover);
  border-color: var(--st-green);
}

/* Login Icons */
.login-icon-child .child-icon-emoji {
  color: var(--st-orange);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  animation: wiggle 1s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(255, 107, 53, 0.3));
}

.login-icon-adult .adult-icon-emoji {
  color: var(--st-blue);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse 2s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(74, 144, 226, 0.3));
}

.login-icon-adult i {
  color: var(--st-blue);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes wiggle {
  0%, 7% { transform: rotateZ(0); }
  15% { transform: rotateZ(-15deg); }
  20% { transform: rotateZ(10deg); }
  25% { transform: rotateZ(-10deg); }
  30% { transform: rotateZ(6deg); }
  35% { transform: rotateZ(-4deg); }
  40%, 100% { transform: rotateZ(0); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Game Buttons */
.btn-game-child {
  background: linear-gradient(145deg, var(--st-orange), var(--st-red));
  border: none;
  border-radius: var(--st-border-radius);
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-game-child:hover {
  background: linear-gradient(145deg, var(--st-red), var(--st-orange));
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.6);
  color: white;
}

.btn-game-adult {
  background: linear-gradient(145deg, var(--st-blue), var(--st-green));
  border: none;
  border-radius: var(--st-border-radius);
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-game-adult:hover {
  background: linear-gradient(145deg, var(--st-green), var(--st-blue));
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.6);
  color: white;
}

.game-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.game-button:hover::before {
  left: 100%;
}

/* Card Titles and Text */
.login-card-child .card-title {
  color: var(--st-orange);
  font-family: var(--st-font-game);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.login-card-adult .card-title {
  color: var(--st-blue);
  font-family: var(--st-font-game);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.card-text {
  font-family: var(--st-font-primary);
  color: #555;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .logo-lg { max-width: 200px; }
  .game-title { font-size: 2rem; }
  .login-card-child, .login-card-adult {
    margin-bottom: 2rem;
  }
}

/* Child Login Page Styles */
.child-login-page {
  background: linear-gradient(135deg, #ff6b35 0%, #ffd700 50%, #4caf50 100%);
  position: relative;
  overflow: hidden;
}

.child-login-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  animation: float 4s ease-in-out infinite;
}

.child-login-card {
  background: linear-gradient(145deg, #fff 0%, #fff8f0 100%);
  border: 4px solid var(--st-orange);
  border-radius: var(--st-border-radius-lg);
  position: relative;
  overflow: hidden;
}

.child-login-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--st-orange), var(--st-yellow), var(--st-green), var(--st-blue));
  border-radius: var(--st-border-radius-lg);
  z-index: -1;
  animation: rainbow-border 3s linear infinite;
}

@keyframes rainbow-border {
  0% { filter: hue-rotate(0deg); }
  100% { filter: hue-rotate(360deg); }
}

.child-login-title {
  color: var(--st-orange);
  font-family: var(--st-font-game);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.child-icon-large {
  font-size: 4rem;
  animation: bounce 2s ease-in-out infinite;
  filter: drop-shadow(0 0 15px rgba(255, 107, 53, 0.4));
}

/* Code Input Styles */
.code-input-container {
  max-width: 350px;
  margin: 0 auto;
}

.code-input-box {
  width: 60px;
  height: 60px;
  font-size: 1.5rem;
  border: 3px solid var(--st-orange);
  border-radius: var(--st-border-radius);
  background: linear-gradient(145deg, #fff, #f8f9fa);
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);
}

.code-input-box:focus {
  border-color: var(--st-yellow);
  box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25), 0 6px 20px rgba(255, 107, 53, 0.4);
  transform: scale(1.05);
  background: linear-gradient(145deg, #fff, #fffbf0);
}

.code-input-box:not(:placeholder-shown) {
  background: linear-gradient(145deg, var(--st-yellow), #fff8dc);
  color: var(--st-orange);
  border-color: var(--st-green);
}

/* Adult Login Page Styles */
.adult-login-page {
  background: var(--st-bg-primary);
  position: relative;
}

.adult-login-card {
  background: linear-gradient(145deg, #fff 0%, #f8f9ff 100%);
  border: 3px solid var(--st-blue);
  border-radius: var(--st-border-radius-lg);
}

.adult-login-title {
  color: var(--st-blue);
  font-family: var(--st-font-primary);
  font-weight: 700;
}

.adult-icon {
  color: var(--st-blue);
  animation: pulse 2s ease-in-out infinite;
}

.adult-login-btn {
  background: linear-gradient(145deg, var(--st-blue), var(--st-green));
  border: none;
  border-radius: var(--st-border-radius);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
  transition: all 0.3s ease;
}

.adult-login-btn:hover {
  background: linear-gradient(145deg, var(--st-green), var(--st-blue));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.6);
  color: white;
}

/* Adult Register Button */
.adult-register-btn {
  background: linear-gradient(145deg, var(--st-blue), var(--st-green));
  border: none;
  border-radius: var(--st-border-radius);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
  transition: all 0.3s ease;
  color: white;
}

.adult-register-btn:hover {
  background: linear-gradient(145deg, var(--st-green), var(--st-blue));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.6);
  color: white;
}

/* Google Login Button */
.google-login-btn {
  border: 2px solid #dadce0;
  border-radius: var(--st-border-radius);
  transition: all 0.3s ease;
  background: white;
}

.google-login-btn:hover {
  border-color: #4285f4;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
  background: #f8f9fa;
}

.google-icon svg {
  transition: transform 0.3s ease;
}

.google-login-btn:hover .google-icon svg {
  transform: scale(1.1);
}

/* Form Enhancements */
.form-control:focus {
  border-color: var(--st-blue);
  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.input-group .btn-outline-secondary {
  border-color: #ced4da;
}

.input-group .btn-outline-secondary:hover {
  background-color: var(--st-blue);
  border-color: var(--st-blue);
  color: white;
}

/* Family Setup Page Theme Extensions */
/* Building on existing SuperTarefa Nintendo-style theme */

:root {
  /* Family Setup specific colors */
  --st-family-primary: var(--st-blue);
  --st-family-secondary: var(--st-green);
  --st-family-accent: var(--st-orange);
  --st-family-bg: linear-gradient(135deg, #f8f9ff 0%, #fff 100%);
  
  /* Setup progress colors */
  --st-progress-bg: #e9ecef;
  --st-progress-fill: var(--st-green);
  
  /* List item colors */
  --st-list-item-bg: #fff;
  --st-list-item-hover: #f8f9fa;
  --st-list-item-border: #dee2e6;
}

/* Family Setup Page Layout */
.family-setup-page {
  background: var(--st-bg-primary);
  min-height: 100vh;
}

.family-setup-card {
  background: var(--st-family-bg);
  border: 3px solid var(--st-family-primary);
  border-radius: var(--st-border-radius-lg);
  box-shadow: var(--st-shadow-game);
}

/* Setup Sections */
.setup-section {
  border-bottom: 2px solid var(--st-list-item-border);
  padding: 2rem 0;
}

.setup-section:last-child {
  border-bottom: none;
}

.setup-section-title {
  color: var(--st-family-primary);
  font-family: var(--st-font-game);
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

/* Family Name Input */
.family-name-input {
  border: 2px solid var(--st-family-primary);
  border-radius: var(--st-border-radius);
  font-size: 1.25rem;
  padding: 0.75rem 1rem;
}

.family-name-input:focus {
  border-color: var(--st-family-accent);
  box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
}

/* Children and Guardian Lists */
.family-member-list {
  background: transparent;
  border: none;
}

.family-member-item {
  background: var(--st-list-item-bg);
  border: 2px solid var(--st-list-item-border);
  border-radius: var(--st-border-radius);
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.family-member-item:hover {
  background: var(--st-list-item-hover);
  border-color: var(--st-family-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Add Member Forms */
.add-member-form {
  background: var(--st-list-item-bg);
  border: 2px dashed var(--st-family-primary);
  border-radius: var(--st-border-radius);
  padding: 1.5rem;
  margin-bottom: 1rem;
}

/* Action Buttons */
.setup-action-btn {
  border-radius: var(--st-border-radius);
  font-weight: 600;
  padding: 0.75rem 2rem;
  transition: all 0.3s ease;
}

.setup-primary-btn {
  background: linear-gradient(145deg, var(--st-family-primary), var(--st-family-secondary));
  border: none;
  color: white;
}

.setup-primary-btn:hover {
  background: linear-gradient(145deg, var(--st-family-secondary), var(--st-family-primary));
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
  color: white;
}

.setup-secondary-btn {
  background: transparent;
  border: 2px solid var(--st-family-primary);
  color: var(--st-family-primary);
}

.setup-secondary-btn:hover {
  background: var(--st-family-primary);
  color: white;
}

/* Progress Indicator */
.setup-progress {
  height: 8px;
  background: var(--st-progress-bg);
  border-radius: 4px;
  overflow: hidden;
}

.setup-progress-fill {
  background: var(--st-progress-fill);
  height: 100%;
  transition: width 0.5s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .logo-lg { max-width: 200px; }
  .game-title { font-size: 2rem; }
  .login-card-child, .login-card-adult {
    margin-bottom: 2rem;
  }
  
  .code-input-box {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
  
  .code-input-container {
    gap: 0.5rem !important;
  }
  
  .child-icon-large {
    font-size: 3rem;
  }
  
  .setup-section {
    padding: 1.5rem 0;
  }
  
  .setup-action-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .family-setup-card .card-body {
    padding: 2rem 1.5rem;
  }
}

/* Adult Dashboard theme extensions */
/* Building on existing SuperTarefa Nintendo-style theme */

:root {
  /* Dashboard specific colors */
  --st-dashboard-bg: linear-gradient(135deg, #f8f9ff 0%, #fff 100%);
  --st-dashboard-header-bg: var(--st-blue);
  --st-dashboard-card-bg: #fff;
  --st-dashboard-card-hover: #f8f9fa;
  
  /* Navigation colors */
  --st-nav-active: var(--st-blue);
  --st-nav-inactive: #6c757d;
  --st-nav-mobile-bg: #fff;
  
  /* Card type colors */
  --st-card-primary: var(--st-blue);
  --st-card-secondary: var(--st-green);
  --st-card-accent: var(--st-orange);
  
  /* Badge colors */
  --st-badge-notification: var(--st-red);
  --st-badge-success: var(--st-green);
  --st-badge-warning: var(--st-yellow);
}

/* Dashboard Layout */
.adult-dashboard {
  background: var(--st-dashboard-bg);
  min-height: 100vh;
  padding-bottom: 80px; /* Space for mobile navbar */
}

.dashboard-header {
  background: var(--st-dashboard-header-bg);
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-main {
  padding: 2rem 0;
}

/* Navigation Cards */
.nav-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

.navigation-card {
  background: var(--st-dashboard-card-bg);
  border: 3px solid transparent;
  border-radius: var(--st-border-radius-lg);
  padding: 2rem;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.navigation-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: var(--st-shadow-hover);
  background: var(--st-dashboard-card-hover);
}

.navigation-card.card-primary {
  border-color: var(--st-card-primary);
}

.navigation-card.card-primary:hover {
  border-color: var(--st-card-primary);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

.navigation-card.card-secondary {
  border-color: var(--st-card-secondary);
}

.navigation-card.card-secondary:hover {
  border-color: var(--st-card-secondary);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.navigation-card.card-accent {
  border-color: var(--st-card-accent);
}

.navigation-card.card-accent:hover {
  border-color: var(--st-card-accent);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  animation: float 3s ease-in-out infinite;
}

.card-title {
  font-family: var(--st-font-game);
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--st-blue);
}

.card-description {
  color: #6c757d;
  margin-bottom: 1rem;
}

.card-count-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--st-badge-notification);
  color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}

/* Mobile Bottom Navbar */
.mobile-navbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--st-nav-mobile-bg);
  border-top: 2px solid var(--st-list-item-border);
  padding: 0.5rem 0;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  color: var(--st-nav-inactive);
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: var(--st-border-radius);
  position: relative;
}

.mobile-nav-item.active {
  color: var(--st-nav-active);
  background: rgba(74, 144, 226, 0.1);
}

.mobile-nav-item:hover {
  color: var(--st-nav-active);
  background: rgba(74, 144, 226, 0.05);
}

.mobile-nav-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.mobile-nav-label {
  font-size: 0.75rem;
  font-weight: 500;
}

.mobile-nav-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background: var(--st-badge-notification);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.6rem;
  font-weight: bold;
}

/* Stats Overview */
.stats-overview {
  background: var(--st-dashboard-card-bg);
  border-radius: var(--st-border-radius-lg);
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: var(--st-blue);
  font-family: var(--st-font-game);
}

.stat-label {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .navigation-card {
    padding: 1.5rem;
  }
  
  .card-icon {
    font-size: 2.5rem;
  }
  
  .dashboard-main {
    padding: 1rem 0;
  }
}

@media (min-width: 769px) {
  .mobile-navbar {
    display: none;
  }
  
  .adult-dashboard {
    padding-bottom: 2rem;
  }
}

/* Chat page theme extensions */
/* Building on existing SuperTarefa Nintendo-style theme */

:root {
  /* Chat specific colors */
  --st-chat-bg: linear-gradient(135deg, #f8f9ff 0%, #fff 100%);
  --st-chat-header-bg: var(--st-blue);
  --st-chat-bubble-sent: var(--st-blue);
  --st-chat-bubble-received: #e9ecef;
  --st-chat-input-bg: #fff;
  
  /* Status colors */
  --st-status-online: #28a745;
  --st-status-away: #ffc107;
  --st-status-offline: #6c757d;
  --st-status-busy: #dc3545;
  
  /* Call interface colors */
  --st-call-bg: rgba(0, 0, 0, 0.9);
  --st-call-controls-bg: rgba(255, 255, 255, 0.1);
  --st-call-end-btn: #dc3545;
  --st-call-accept-btn: #28a745;
  
  /* Swipe action colors */
  --st-swipe-archive: #6c757d;
  --st-swipe-delete: #dc3545;
  --st-swipe-mute: #ffc107;
  --st-swipe-read: #28a745;
}

/* Chat Page Layout */
.chat-page {
  background: var(--st-chat-bg);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 80px; /* Space for mobile navbar */
  position: relative;
  overflow: hidden;
}

.chat-header {
  background: var(--st-chat-header-bg);
  padding: 0.75rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: all 0.3s ease;
}

/* Mobile-optimized chat header */
@media (max-width: 768px) {
  .chat-header {
    padding: 0.5rem 0;
    min-height: 60px;
  }
}

.chat-main {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Conversation area optimizations */
.conversation-messages {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1rem;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Mobile conversation optimizations */
@media (max-width: 768px) {
  .conversation-messages {
    padding: 0.75rem 0.5rem;
    padding-bottom: 1rem;
  }
}

/* Filter Pills */
.chat-filter-pills {
  background: #fff;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  overflow-x: auto;
  white-space: nowrap;
}

.filter-pill {
  display: inline-block;
  padding: 0.5rem 1rem;
  margin-right: 0.5rem;
  border: 2px solid var(--st-blue);
  border-radius: var(--st-border-radius-lg);
  background: transparent;
  color: var(--st-blue);
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
}

.filter-pill.active {
  background: var(--st-blue);
  color: white;
}

.filter-pill:hover {
  background: var(--st-blue);
  color: white;
  transform: translateY(-2px);
}

/* Chat List */
.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.chat-item {
  border: none;
  border-bottom: 1px solid #dee2e6;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chat-item:hover {
  background: #f8f9fa;
  transform: translateX(5px);
}

.chat-item.family-chat {
  background: linear-gradient(145deg, #fff 0%, #f0f8ff 100%);
  border-left: 4px solid var(--st-blue);
}

.chat-item.private-chat {
  background: #fff;
}

.chat-item.unread {
  border-left: 4px solid var(--st-orange);
  background: #fff8f0;
}

.chat-item.archived {
  opacity: 0.6;
  background: #f8f9fa;
}

/* Chat Item Content */
.chat-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  background: var(--st-blue);
  color: white;
  position: relative;
}

.chat-info {
  flex: 1;
  min-width: 0;
}

.chat-name {
  font-weight: 600;
  color: var(--st-blue);
  margin-bottom: 0.25rem;
  font-family: var(--st-font-game);
}

.chat-last-message {
  color: #6c757d;
  font-size: 0.9rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-meta {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.chat-time {
  color: #6c757d;
  font-size: 0.8rem;
}

.chat-unread-badge {
  background: var(--st-orange);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
}

/* Online Status Indicator */
.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.online-status.online {
  background: var(--st-status-online);
}

.online-status.away {
  background: var(--st-status-away);
}

.online-status.offline {
  background: var(--st-status-offline);
}

.online-status.busy {
  background: var(--st-status-busy);
}

/* Swipe Actions */
.swipe-actions {
  position: absolute;
  top: 0;
  right: -200px;
  height: 100%;
  width: 200px;
  display: flex;
  transition: right 0.3s ease;
}

.chat-item.swiped .swipe-actions {
  right: 0;
}

.swipe-action {
  flex: 1;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.swipe-action.archive {
  background: var(--st-swipe-archive);
}

.swipe-action.delete {
  background: var(--st-swipe-delete);
}

.swipe-action.mute {
  background: var(--st-swipe-mute);
}

.swipe-action.read {
  background: var(--st-swipe-read);
}

/* Call Interface */
.call-interface {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--st-call-bg);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.call-participants {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.call-avatar .avatar-large {
  font-size: 4rem;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: var(--st-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.call-controls {
  background: var(--st-call-controls-bg);
  border-radius: var(--st-border-radius-lg);
  padding: 1rem;
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.call-control-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  color: white;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.2);
}

.call-control-btn.end-call {
  background: var(--st-call-end-btn);
}

.call-control-btn.accept-call {
  background: var(--st-call-accept-btn);
}

.call-control-btn.active {
  background: var(--st-orange);
}

.call-control-btn:hover {
  transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-item {
    padding: 0.75rem;
  }
  
  .chat-avatar {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .call-controls {
    padding: 0.75rem;
    gap: 0.75rem;
  }
  
  .call-control-btn {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
  
  .call-avatar .avatar-large {
    font-size: 3rem;
    width: 80px;
    height: 80px;
  }
}

/* Message Input Fixed Positioning */
.message-input-fixed {
  position: fixed;
  bottom: 80px; /* Above mobile navbar */
  left: 0;
  right: 0;
  background: var(--st-chat-input-bg);
  border-top: 1px solid #dee2e6;
  padding: 1rem;
  z-index: 999;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  transition: bottom 0.3s ease;
  backdrop-filter: blur(10px);
}

/* Mobile message input optimizations */
@media (max-width: 768px) {
  .message-input-fixed {
    padding: 0.75rem 0.5rem;
    border-top: 2px solid #dee2e6;
  }

  /* Adjust for virtual keyboard */
  .message-input-fixed.keyboard-open {
    bottom: 0;
  }
}

/* Child Dashboard theme extensions */
/* Building on existing SuperTarefa Nintendo-style theme */

:root {
  /* Child Dashboard specific colors */
  --st-child-bg: linear-gradient(135deg, #ff6b35 0%, #ffd700 50%, #4caf50 100%);
  --st-child-header-bg: linear-gradient(145deg, #fff 0%, #fff8f0 100%);
  --st-child-card-bg: linear-gradient(145deg, #fff 0%, #fff8f0 100%);
  --st-child-card-hover: #f8f9fa;

  /* Child Navigation colors */
  --st-child-nav-active: var(--st-orange);
  --st-child-nav-inactive: #6c757d;
  --st-child-nav-mobile-bg: #fff;

  /* Child Card type colors */
  --st-child-card-primary: var(--st-orange);
  --st-child-card-secondary: var(--st-green);
  --st-child-card-accent: var(--st-yellow);

  /* Child Badge colors */
  --st-child-badge-notification: var(--st-orange);
  --st-child-badge-success: var(--st-green);
  --st-child-badge-warning: var(--st-yellow);

  /* Child Chat specific colors */
  --st-child-chat-bg: linear-gradient(135deg, #ff6b35 0%, #ffd700 50%, #4caf50 100%);
  --st-child-chat-header-bg: linear-gradient(145deg, #fff 0%, #fff8f0 100%);
  --st-child-chat-bubble-sent: linear-gradient(135deg, #ff6b35 0%, #ff8a65 100%);
  --st-child-chat-bubble-received: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
  --st-child-chat-input-bg: linear-gradient(145deg, #fff 0%, #fff8f0 100%);
}

/* Child Dashboard Layout */
.child-dashboard {
  background: #f8f9fa;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  padding-bottom: 80px; /* Space for mobile navbar */
}


.child-dashboard-content {
  position: relative;
  z-index: 1;
}

.child-header {
  background: var(--st-child-header-bg);
  border: 4px solid var(--st-orange);
  border-radius: var(--st-border-radius-lg);
  position: relative;
  overflow: hidden;
}

.child-header::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--st-orange), var(--st-yellow), var(--st-green), var(--st-blue));
  border-radius: var(--st-border-radius-lg);
  z-index: -1;
  animation: rainbow-border 3s linear infinite;
}

.child-title {
  color: var(--st-orange);
  font-family: var(--st-font-game);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.child-icon-large {
  font-size: 4rem;
  animation: bounce 2s ease-in-out infinite;
  filter: drop-shadow(0 0 15px rgba(255, 107, 53, 0.4));
}

/* Child Navigation Cards */
.child-navigation-card {
  background: var(--st-child-card-bg);
  border: 3px solid var(--st-orange);
  border-radius: var(--st-border-radius-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.child-navigation-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: var(--st-shadow-hover);
  border-color: var(--st-yellow);
}

.child-navigation-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.child-navigation-card:hover::before {
  left: 100%;
}

.child-card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  animation: float 3s ease-in-out infinite;
}

.child-card-title {
  font-family: var(--st-font-game);
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--st-orange);
}

.child-card-description {
  color: #6c757d;
  margin-bottom: 1rem;
}

.child-card-count-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--st-orange);
  color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}

/* Child Mobile Bottom Navbar */
.child-mobile-navbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--st-child-nav-mobile-bg);
  border-top: 2px solid #dee2e6;
  padding: 0.5rem 0;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.child-mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  color: var(--st-child-nav-inactive);
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: var(--st-border-radius);
  position: relative;
}

.child-mobile-nav-item.active {
  color: var(--st-child-nav-active);
  background: rgba(255, 107, 53, 0.1);
}

.child-mobile-nav-item:hover {
  color: var(--st-child-nav-active);
  background: rgba(255, 107, 53, 0.05);
}

.child-mobile-nav-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.child-mobile-nav-label {
  font-size: 0.75rem;
  font-weight: 500;
}

.child-mobile-nav-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background: var(--st-child-badge-notification);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.6rem;
  font-weight: bold;
}

/* Child Stats Overview */
.child-stats-overview {
  background: var(--st-child-card-bg);
  border: 3px solid var(--st-orange);
  border-radius: var(--st-border-radius-lg);
  box-shadow: var(--st-shadow-game);
}

.child-stat-item {
  text-align: center;
}

.child-stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: var(--st-orange);
  font-family: var(--st-font-game);
}

.child-stat-label {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .child-dashboard {
    padding-bottom: 80px;
  }
  
  .child-navigation-card {
    padding: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .child-card-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  .child-card-title {
    font-size: 1rem;
    margin-bottom: 0.25rem;
  }
  
  .child-card-description {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
  }
  
  .child-icon-large {
    font-size: 2.5rem;
  }
  
  .child-stat-number {
    font-size: 1.5rem;
  }
  
  .child-stat-label {
    font-size: 0.8rem;
  }
}

@media (min-width: 769px) {
  .child-mobile-navbar {
    display: none;
  }

  .child-dashboard {
    padding-bottom: 2rem;
  }
}

/* Modern Chat Page Design */
.child-chat-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

.child-chat-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: none;
  padding: 1.5rem 1rem;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 24px 24px;
  margin: 0 0.5rem;
}

.child-chat-title {
  color: #2d3748;
  font-weight: 700;
  font-size: 1.4rem;
  margin: 0;
  letter-spacing: -0.025em;
}

.child-chat-subtitle {
  color: #718096;
  font-weight: 500;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.child-chat-avatar {
  font-size: 2.5rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.child-chat-conversation {
  background: transparent;
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 1rem 0.5rem 0;
}

/* Modern Message Bubbles */
.child-message-bubble {
  border-radius: 24px;
  padding: 1rem 1.25rem;
  margin-bottom: 1rem;
  position: relative;
  word-wrap: break-word;
  max-width: 80%;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.child-message-bubble:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.child-message-bubble.sent {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 8px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.child-message-bubble.received {
  background: rgba(255, 255, 255, 0.95);
  color: #2d3748;
  margin-right: auto;
  border-bottom-left-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.child-message-content {
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.child-message-sender {
  font-size: 0.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.child-message-time {
  font-size: 0.75rem;
  opacity: 0.7;
  margin-top: 0.5rem;
  font-weight: 500;
}

.child-message-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 700;
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

/* Modern Message Input - Glass Morphism */
.child-message-input-fixed {
  position: fixed;
  bottom: 80px; /* Above mobile navbar */
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: none;
  padding: 1.5rem 1rem;
  z-index: 999;
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 24px 24px 0 0;
  margin: 0 0.5rem;
}

.child-message-input-container {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
  max-width: 100%;
}

.child-message-input-wrapper {
  flex: 1;
  position: relative;
}

.child-message-input {
  width: 100%;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 28px;
  padding: 16px 60px 16px 20px;
  font-size: 16px; /* Prevents zoom on iOS */
  resize: none;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 56px;
  max-height: 120px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  font-weight: 500;
  color: #2d3748;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.child-message-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 24px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.child-message-input::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

/* Modern Input Buttons */
.child-emoji-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(102, 126, 234, 0.1);
  border: none;
  color: #667eea;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.child-emoji-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.child-audio-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.child-audio-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  border-radius: 50%;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.child-audio-btn:hover::before {
  opacity: 1;
}

.child-audio-btn:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
}

.child-audio-btn:active {
  transform: translateY(-2px) scale(1.02);
}

.child-audio-btn.recording {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  animation: pulse-recording 1.5s ease-in-out infinite;
  box-shadow: 0 8px 24px rgba(255, 107, 107, 0.4);
}

@keyframes pulse-recording {
  0% { transform: scale(1); box-shadow: 0 8px 24px rgba(255, 107, 107, 0.4); }
  50% { transform: scale(1.1); box-shadow: 0 12px 32px rgba(255, 107, 107, 0.6); }
  100% { transform: scale(1); box-shadow: 0 8px 24px rgba(255, 107, 107, 0.4); }
}

.child-send-btn {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  border: none;
  color: white;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 8px 24px rgba(72, 187, 120, 0.3);
  position: relative;
  overflow: hidden;
}

.child-send-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  border-radius: 50%;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.child-send-btn:hover::before {
  opacity: 1;
}

.child-send-btn:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 32px rgba(72, 187, 120, 0.4);
}

.child-send-btn:active {
  transform: translateY(-2px) scale(1.02);
}

.child-send-btn:disabled {
  background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 12px rgba(160, 174, 192, 0.2);
}

/* Modern Chat List */
.child-chat-list {
  background: transparent;
  padding: 1rem 0.5rem;
  flex: 1;
  overflow-y: auto;
}

.child-chat-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  margin-bottom: 1rem;
  padding: 1.25rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.child-chat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.child-chat-item:hover::before {
  opacity: 1;
}

.child-chat-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
}

.child-chat-item:active {
  transform: translateY(-2px) scale(1.01);
}

.child-chat-item-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  position: relative;
  z-index: 1;
}

.child-chat-item-title {
  color: #2d3748;
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.025em;
}

.child-chat-item-subtitle {
  color: #718096;
  font-weight: 500;
  font-size: 0.9rem;
  margin: 0.25rem 0 0;
}

.child-chat-item-time {
  color: #a0aec0;
  font-size: 0.8rem;
  font-weight: 500;
}

.child-chat-item-unread {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 700;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
  animation: pulse-notification 2s ease-in-out infinite;
}

@keyframes pulse-notification {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 1rem;
  margin-bottom: 1rem;
}

.typing-dots {
  display: flex;
  gap: 0.25rem;
  margin-left: 0.5rem;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  animation: typing-bounce 1.4s ease-in-out infinite both;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .child-chat-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .child-chat-header {
    margin: 0;
    border-radius: 0 0 20px 20px;
    padding: 1rem;
  }

  .child-chat-title {
    font-size: 1.2rem;
  }

  .child-chat-avatar {
    font-size: 2rem;
  }

  .child-chat-conversation {
    margin: 0.5rem 0 0;
  }

  .child-message-bubble {
    max-width: 85%;
    padding: 0.875rem 1rem;
    margin-bottom: 0.875rem;
  }

  .child-message-avatar {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .child-message-input-fixed {
    margin: 0;
    border-radius: 20px 20px 0 0;
    padding: 1rem;
  }

  .child-message-input {
    min-height: 48px;
    padding: 14px 55px 14px 18px;
    border-radius: 24px;
  }

  .child-audio-btn,
  .child-send-btn {
    width: 48px;
    height: 48px;
    font-size: 1rem;
  }

  .child-emoji-btn {
    width: 36px;
    height: 36px;
    right: 10px;
    font-size: 1rem;
  }

  .child-chat-list {
    padding: 0.75rem 0.25rem;
  }

  .child-chat-item {
    padding: 1rem;
    margin-bottom: 0.75rem;
    border-radius: 16px;
  }

  .child-chat-item-avatar {
    width: 48px;
    height: 48px;
    font-size: 1.5rem;
  }

  .child-chat-item-title {
    font-size: 1.1rem;
  }

  .messages-container {
    padding: 0.75rem;
  }
}

/* Child Chat Responsive Design */
@media (max-width: 768px) {
  .child-chat-header {
    margin: 0.5rem;
    padding: 1rem;
  }

  .child-chat-avatar {
    font-size: 2.5rem;
  }

  .child-chat-title {
    font-size: 1.3rem;
  }

  .child-chat-subtitle {
    font-size: 0.8rem;
  }

  .child-chat-conversation {
    margin: 0 0.5rem;
    height: calc(100vh - 180px);
  }

  .child-chat-list {
    margin: 0 0.5rem;
  }

  .child-chat-item {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .child-chat-item-avatar {
    width: 45px;
    height: 45px;
    font-size: 1.8rem;
  }

  .child-chat-item-title {
    font-size: 1.1rem;
  }

  .child-message-bubble {
    padding: 0.75rem 1rem;
    margin-bottom: 0.75rem;
  }

  .child-message-content {
    font-size: 1rem;
  }

  .child-message-input-container {
    padding: 0.75rem;
    margin: 0 0.5rem;
  }
}

/* Fun loading animation for child chat */
@keyframes wiggle {
  0%, 7% { transform: rotateZ(0); }
  15% { transform: rotateZ(-15deg); }
  20% { transform: rotateZ(10deg); }
  25% { transform: rotateZ(-10deg); }
  30% { transform: rotateZ(6deg); }
  35% { transform: rotateZ(-4deg); }
  40%, 100% { transform: rotateZ(0); }
}

.child-chat-avatar:hover {
  animation: wiggle 2s ease-in-out;
}

/* Remove sparkle effects */

/* Typing indicator for child chat */
.child-typing-indicator {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  margin: 0.5rem;
  border: 2px solid var(--st-green);
}

.child-typing-dots {
  display: flex;
  gap: 0.25rem;
}

.child-typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--st-orange);
  animation: typing-bounce 1.4s ease-in-out infinite both;
}

.child-typing-dot:nth-child(1) { animation-delay: -0.32s; }
.child-typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.message-input-container {
  max-width: 100%;
  margin: 0;
}

/* Emoji Picker Styles */
.emoji-picker-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1998;
}

.emoji-picker {
  position: fixed;
  bottom: 160px; /* Above message input */
  left: 1rem;
  right: 1rem;
  max-height: 300px;
  background: white;
  border-radius: var(--st-border-radius-lg);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1999;
  display: flex;
  flex-direction: column;
}

.emoji-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  background: var(--st-blue);
  color: white;
  border-radius: var(--st-border-radius-lg) var(--st-border-radius-lg) 0 0;
}

.emoji-picker-header .btn-close {
  filter: invert(1);
}

.emoji-picker-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.emoji-category {
  margin-bottom: 1.5rem;
}

.emoji-category:last-child {
  margin-bottom: 0;
}

.emoji-category-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--st-blue);
  margin-bottom: 0.5rem;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid #dee2e6;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 0.25rem;
}

.emoji-button {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  border-radius: var(--st-border-radius);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-button:hover {
  background: #f8f9fa;
  transform: scale(1.1);
}

.emoji-button:active {
  transform: scale(0.95);
}

/* Chat Conversation Styles */
.chat-conversation {
  height: 100vh;
  padding-bottom: 160px; /* Space for fixed message input + mobile navbar */
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding-bottom: 80px; /* Space for mobile navbar */
}

.conversation-header {
  background: var(--st-chat-header-bg);
  color: white;
  padding: 1rem;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.conversation-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: #f8f9fa;
}

/* Message Bubble Styles */
.message-bubble {
  max-width: 90%;
  padding: 0.75rem 1rem;
  border-radius: var(--st-border-radius-lg);
  margin-bottom: 0.5rem;
  position: relative;
  word-wrap: break-word;
}

.message-bubble.sent {
  background: var(--st-chat-bubble-sent);
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 0.25rem;
}

.message-bubble.received {
  background: var(--st-chat-bubble-received);
  color: #333;
  margin-right: auto;
  border-bottom-left-radius: 0.25rem;
}

.message-sender {
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  opacity: 0.8;
}

.message-content {
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.message-info {
  font-size: 0.7rem;
  opacity: 0.7;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--st-blue);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Message Input Styles */
.message-input-container {
  background: var(--st-chat-input-bg);
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  position: sticky;
  bottom: 80px; /* Above mobile navbar */
}

.message-input {
  border: 2px solid #dee2e6;
  border-radius: var(--st-border-radius-lg);
  padding: 0.75rem 1rem;
  resize: none;
  min-height: 44px;
  max-height: 120px;
  font-family: inherit;
}

.message-input:focus {
  border-color: var(--st-blue);
  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.btn-send-active {
  background: var(--st-blue);
  border-color: var(--st-blue);
  transform: scale(1.05);
}

.typing-indicator {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Mobile touch optimizations */
.mobile-touch-target {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Mobile send button */
.btn-send-mobile {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  touch-action: manipulation;
}

.btn-send-mobile:active {
  transform: scale(0.95);
}

/* Swipe gestures */
.swipe-container {
  position: relative;
  overflow: hidden;
}

.swipe-actions {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  display: flex;
  align-items: center;
  background: #dc3545;
  color: white;
  padding: 0 1rem;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.swipe-actions.visible {
  transform: translateX(0);
}

/* Pull to refresh */
.pull-to-refresh {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--st-primary);
  color: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  opacity: 0;
}

.pull-to-refresh.visible {
  opacity: 1;
  top: 10px;
}

/* Responsive adjustments for conversation */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 98%;
    margin-bottom: 0.75rem;
    padding: 0.75rem 1rem;
  }

  .conversation-messages {
    padding: 0.75rem 0.5rem;
    padding-bottom: 1rem;
  }

  .message-input-container {
    padding: 0.75rem 0.5rem;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }

  /* Larger touch targets for mobile */
  .btn {
    min-height: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .input-group .btn {
    min-width: 48px;
    font-size: 1.1rem;
  }

  /* Mobile message input */
  .message-input {
    font-size: 16px; /* Prevents zoom on iOS */
    border-radius: 25px;
    padding: 12px 16px;
  }

  /* Chat header mobile */
  .chat-header .container {
    padding: 0 0.5rem;
  }

  .chat-header .btn {
    min-width: 44px;
    min-height: 44px;
  }

  /* Conversation header mobile */
  .conversation-header {
    padding: 0.75rem 0.5rem;
  }

  .conversation-header .btn-group .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }
}

/* Mobile animations and transitions */
@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Message animations */
.message-bubble {
  animation: slideInUp 0.3s ease-out;
}

.message-bubble.sent {
  animation: slideInRight 0.3s ease-out;
}

.message-bubble.received {
  animation: slideInLeft 0.3s ease-out;
}

/* Chat item animations */
.chat-item {
  animation: fadeIn 0.3s ease-out;
  transition: all 0.3s ease;
}

.chat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chat-item:active {
  transform: translateY(0);
}

/* Button press animations */
.btn:active {
  transform: scale(0.95);
}

.mobile-touch-target:active {
  transform: scale(0.95);
  background-color: rgba(0, 0, 0, 0.05);
}

/* Loading states */
.loading-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Smooth scrolling */
.conversation-messages {
  scroll-behavior: smooth;
}

/* Focus states for accessibility */
.message-input:focus {
  outline: none;
  border-color: var(--st-primary);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .message-bubble {
    border: 2px solid currentColor;
  }

  .btn {
    border: 2px solid currentColor;
  }
}

/* ===================================
   MODERN CHAT APP STYLES (Like Telegram/WhatsApp)
   =================================== */

/* Main App Container */
.modern-chat-app {
  height: 100vh;
  background: #f5f5f5;
  overflow: hidden;
}

/* Desktop Layout */
.desktop-chat-layout {
  height: 100vh;
  background: #f5f5f5;
}

/* Chat List Panel (Left Side) */
.chat-list-panel {
  width: 380px;
  background: white;
  border-right: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.chat-list-header {
  border-bottom: 1px solid #e5e5e5;
  background: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Search Container */
.search-container {
  background: white;
}

.search-input-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 20px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 40px;
}

.search-icon {
  color: #999;
  margin-right: 10px;
  font-size: 14px;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 14px;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.search-edit-btn {
  background: none;
  border: none;
  color: #6366f1;
  font-size: 14px;
  padding: 0;
  margin-left: 10px;
}

/* Chat List Container */
.chat-list-container {
  flex: 1;
  overflow-y: auto;
  background: white;
}

/* Modern Chat Item */
.modern-chat-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.modern-chat-item:hover {
  background: #f8f9fa;
}

.modern-chat-item.active {
  background: #e8f0fe;
  border-right: 3px solid #6366f1;
}

.modern-chat-item.mobile {
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
}

/* Chat Item Avatar */
.chat-item-avatar {
  position: relative;
  margin-right: 12px;
  flex-shrink: 0;
}

.avatar-img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #4ade80;
  border: 2px solid white;
  border-radius: 50%;
}

/* Chat Item Content */
.chat-item-content {
  flex: 1;
  min-width: 0;
}

.chat-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.chat-item-name {
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-item-time {
  font-size: 12px;
  color: #9ca3af;
  flex-shrink: 0;
  margin-left: 8px;
}

.chat-item-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-text {
  font-size: 13px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.unread-badge {
  background: #6366f1;
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  margin-left: 8px;
  flex-shrink: 0;
}

/* Add Chat Button */
.add-chat-container {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 100;
}

.add-chat-btn {
  width: 56px;
  height: 56px;
  background: #6366f1;
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.add-chat-btn:hover {
  background: #5856eb;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
}

/* Conversation Panel (Right Side) */
.conversation-panel {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* No Chat Selected State */
.no-chat-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.no-chat-content {
  text-align: center;
  max-width: 300px;
}

/* Mobile Views */
.mobile-conversation-view {
  height: 100vh;
  background: white;
}

.mobile-chat-list {
  height: 100vh;
  background: white;
  display: flex;
  flex-direction: column;
}

.mobile-header {
  background: white;
  border-bottom: 1px solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 10;
}

.mobile-chat-items {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 80px; /* Space for navbar */
}

/* ===================================
   CHILD iMESSAGE STYLES (Updated)
   =================================== */

/* iMessage Conversation Container */
.child-imessage-conversation {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f0f0;
  position: relative;
  overflow: hidden;
}

/* Mobile conversation styling */
@media (max-width: 767px) {
  .child-imessage-conversation {
    background: #f0f0f0;
    border-radius: 20px 20px 0 0;
    margin: 10px;
    margin-bottom: 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  /* Full screen mobile conversation */
  .mobile-conversation-view .child-imessage-conversation {
    margin: 0;
    border-radius: 0;
    height: 100vh;
  }
}

/* Header */
.child-imessage-header {
  background: white;
  border-bottom: 1px solid #e5e5e5;
  padding: 1rem 0;
  position: relative;
  z-index: 10;
}

/* Mobile header styling */
@media (max-width: 767px) {
  .child-imessage-header {
    border-radius: 20px 20px 0 0;
    padding: 1.5rem 0 1rem 0;
  }
}

.child-imessage-back-btn {
  background: none;
  border: none;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6366f1;
  font-size: 1.2rem;
  font-weight: bold;
  transition: all 0.3s ease;
  border-radius: 50%;
}

.child-imessage-back-btn:hover {
  background: #f3f4f6;
}

.child-imessage-back-btn:active {
  transform: scale(0.95);
}

.child-imessage-chat-title {
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  text-align: left;
}

.child-imessage-chat-subtitle {
  color: #4ade80;
  font-size: 0.85rem;
  margin: 0;
  font-weight: 500;
  text-align: left;
}

.child-imessage-call-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.child-imessage-call-btn {
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  color: #6b7280;
}

.child-imessage-call-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.child-imessage-call-btn.video {
  color: #6366f1;
}

/* Conversation Avatar */
.conversation-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

/* Messages Container */
.child-imessage-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  padding-bottom: 100px; /* Space for input only */
  position: relative;
  z-index: 1;
  background: #f0f0f0;
}

/* Mobile messages styling */
@media (max-width: 767px) {
  .child-imessage-messages-container {
    background: #f0f0f0;
    padding: 1rem 1.5rem;
  }
}

/* Loading Animation */
.child-imessage-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: white;
}

.child-imessage-loading-dots {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.child-imessage-loading-dots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  animation: bounce 1.4s ease-in-out infinite both;
}

.child-imessage-loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.child-imessage-loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.child-imessage-loading-dots span:nth-child(3) { animation-delay: 0s; }

/* Message Bubble Container */
.child-imessage-bubble-container {
  margin-bottom: 1rem;
}

.child-imessage-bubble-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  max-width: 85%;
}

.child-imessage-bubble-wrapper.own-message {
  margin-left: auto;
  flex-direction: row-reverse;
}

.child-imessage-bubble-wrapper.received-message {
  margin-right: auto;
}

/* Avatar */
.child-imessage-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FFD93D, #FF6B6B);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border: 3px solid white;
  flex-shrink: 0;
}

.child-imessage-avatar-emoji {
  font-size: 1.2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Message Bubble Group */
.child-imessage-bubble-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.child-imessage-sender-name {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.25rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Message Bubble */
.child-imessage-bubble {
  position: relative;
  padding: 14px 18px;
  border-radius: 20px;
  max-width: 75%;
  word-wrap: break-word;
  animation: messageSlideIn 0.3s ease-out;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.child-imessage-bubble.sent {
  background: linear-gradient(135deg, #7c3aed, #a855f7) !important;
  border-bottom-right-radius: 6px;
  margin-left: auto;
  margin-right: 0;
  text-align: left;
}

.child-imessage-bubble.received {
  background: white !important;
  border-bottom-left-radius: 6px;
  margin-left: 0;
  margin-right: auto;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  text-align: left;
}

.child-imessage-content {
  font-size: 15px;
  line-height: 1.4;
  font-weight: 400;
  margin: 0;
  text-align: left;
}

.child-imessage-bubble.sent .child-imessage-content {
  color: white;
  text-align: left;
}

.child-imessage-bubble.received .child-imessage-content {
  color: #374151;
  text-align: left;
}

/* Force correct bubble colors with highest specificity */
.child-imessage-conversation .child-imessage-messages-container .child-imessage-bubble.sent {
  background: linear-gradient(135deg, #7c3aed, #a855f7) !important;
  background-color: #7c3aed !important;
}

.child-imessage-conversation .child-imessage-messages-container .child-imessage-bubble.received {
  background: white !important;
  background-color: white !important;
}

/* Additional specificity for message bubbles */
div.child-imessage-conversation div.child-imessage-bubble.sent {
  background: linear-gradient(135deg, #7c3aed, #a855f7) !important;
}

div.child-imessage-conversation div.child-imessage-bubble.received {
  background: white !important;
}

/* Message Tail */
.child-imessage-tail {
  position: absolute;
  bottom: 0;
  width: 20px;
  height: 20px;
  background: inherit;
  border: inherit;
}

.child-imessage-tail.sent-tail {
  right: -10px;
  border-radius: 0 0 20px 0;
  border-top: none;
  border-left: none;
}

.child-imessage-tail.received-tail {
  left: -10px;
  border-radius: 0 0 0 20px;
  border-top: none;
  border-right: none;
}

/* Message Info */
.child-imessage-info {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #9ca3af;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.child-imessage-info.sent {
  justify-content: flex-end;
  text-align: right;
}

.child-imessage-info.received {
  justify-content: flex-start;
  text-align: left;
}

.child-imessage-time {
  color: #9ca3af;
  font-size: 12px;
  margin-top: 4px;
}

.child-imessage-status {
  font-size: 0.8rem;
  color: #7c3aed;
}

/* Message group styling */
.child-imessage-message-group {
  margin-bottom: 1rem;
  text-align: left;
}

.child-imessage-message-group.sent {
  text-align: left;
}

.child-imessage-message-group.received {
  text-align: left;
}

/* Typing Indicator */
.child-imessage-typing-indicator {
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  margin-bottom: 1rem;
  max-width: 85%;
}

.child-imessage-typing-bubble {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25px;
  padding: 1rem 1.25rem;
  border-bottom-left-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

.child-imessage-typing-dots {
  display: flex;
  gap: 0.3rem;
}

.child-imessage-typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #666;
  animation: typingDots 1.4s ease-in-out infinite both;
}

.child-imessage-typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.child-imessage-typing-dots span:nth-child(2) { animation-delay: -0.16s; }
.child-imessage-typing-dots span:nth-child(3) { animation-delay: 0s; }

/* Message Input Container */
.child-imessage-input-container {
  position: relative;
  background: white;
  border-top: 1px solid #e5e5e5;
  padding: 1rem 1.5rem;
  z-index: 10;
}

.child-imessage-input-fixed {
  position: fixed;
  bottom: 0; /* No mobile navbar */
  left: 0;
  right: 0;
  z-index: 1000;
}

/* Desktop input positioning */
@media (min-width: 768px) {
  .desktop-chat-layout .child-imessage-input-fixed {
    position: relative;
    bottom: auto;
    left: auto;
    right: auto;
  }
}

/* Mobile input styling */
@media (max-width: 767px) {
  .child-imessage-input-container {
    padding: 1rem 1.5rem 2rem 1.5rem;
    background: white;
  }
}

/* Quick Emojis */
.child-imessage-quick-emojis {
  display: none; /* Hidden for clean design */
}

/* Input Wrapper */
.child-imessage-input-wrapper {
  position: relative;
}

.child-imessage-input-box {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: #f8f9fa;
  border-radius: 25px;
  padding: 0.5rem 1rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.child-imessage-input-box:focus-within {
  border-color: #7c3aed;
  background: white;
}

/* Emoji Button */
.child-imessage-emoji-btn {
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
  color: #6b7280;
}

.child-imessage-emoji-btn:hover {
  background: #e5e7eb;
}

/* Text Input */
.child-imessage-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 1rem;
  line-height: 1.4;
  padding: 0.5rem 0;
  min-height: 24px;
  max-height: 120px;
  font-family: inherit;
  color: #333;
}

.child-imessage-input::placeholder {
  color: #999;
  font-style: italic;
}

/* Send Button */
.child-imessage-send-btn {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  opacity: 0.5;
  transform: scale(0.8);
}

.child-imessage-send-btn.active {
  opacity: 1;
  transform: scale(1);
}

.child-imessage-send-btn:hover.active {
  background: linear-gradient(135deg, #6d28d9, #9333ea);
  transform: scale(1.05);
}

.child-imessage-send-icon {
  color: white;
  font-size: 1.1rem;
  font-weight: bold;
  transform: rotate(-45deg);
}

/* Send Animation */
.child-imessage-send-animation {
  animation: sendPulse 0.3s ease-out;
}

/* Background Elements - Hidden for clean design */
.child-imessage-input-bg-elements {
  display: none;
}

/* ===================================
   CHILD REWARDS PAGE STYLES
   =================================== */

/* Main Container */
.child-rewards-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 80px; /* Space for navbar */
}

/* Header */
.child-rewards-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.child-back-btn {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.child-back-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.child-page-title {
  color: #333;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
  font-family: var(--st-font-game);
}

.child-page-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

/* Mobile optimizations for page headers */
@media (max-width: 767px) {
  .child-page-title {
    font-size: 1.2rem;
  }

  .child-page-subtitle {
    font-size: 0.9rem;
  }

  .child-trophies-header .container-fluid,
  .child-calendar-header .container-fluid,
  .child-rewards-header .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

.child-points-display {
  display: flex;
  align-items: center;
}

.points-badge {
  background: linear-gradient(135deg, #FFD93D, #FF6B6B);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  font-weight: bold;
  font-size: 1.1rem;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Categories Section */
.child-categories-section {
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem 0;
  backdrop-filter: blur(10px);
}

.categories-scroll {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding: 0.5rem 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.categories-scroll::-webkit-scrollbar {
  display: none;
}

/* Mobile optimizations for categories */
@media (max-width: 767px) {
  .categories-scroll {
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    margin: 0 -1rem;
  }
}

.category-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 20px;
  padding: 12px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 80px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.category-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.category-btn.active {
  background: var(--category-color, linear-gradient(135deg, #4ECDC4, #44A08D));
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.category-icon {
  font-size: 1.5rem;
}

.category-name {
  font-size: 0.8rem;
  font-weight: 600;
}

/* Content Area */
.child-rewards-content {
  padding: 2rem 0;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: white;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h4 {
  color: white;
  font-family: var(--st-font-game);
  margin-bottom: 1rem;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
}

/* Rewards Grid */
.rewards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

/* Reward Card */
.reward-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.reward-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.reward-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  filter: grayscale(0.5);
}

.reward-card.disabled:hover {
  transform: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Reward Header */
.reward-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.reward-category-badge {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.reward-points {
  background: linear-gradient(135deg, #FFD93D, #FF6B6B);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

/* Reward Content */
.reward-content {
  margin-bottom: 1.5rem;
}

.reward-title {
  color: #333;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  font-family: var(--st-font-game);
}

.reward-description {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.4;
  margin: 0;
}

/* Reward Footer */
.reward-footer {
  margin-top: auto;
}

.claim-btn {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: bold;
  font-size: 1rem;
  width: 100%;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.claim-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.insufficient-points {
  background: #f8f9fa;
  color: #6c757d;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  text-align: center;
  border: 2px solid #e9ecef;
}

/* Modal Styles */
.child-modal {
  border-radius: 20px;
  border: 3px solid #FF6B6B;
  overflow: hidden;
}

.child-modal .modal-header {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: white;
  border: none;
  padding: 1.5rem;
}

.child-modal .modal-title {
  font-family: var(--st-font-game);
  font-size: 1.3rem;
  font-weight: bold;
}

.child-modal .modal-body {
  padding: 2rem;
}

.reward-preview {
  text-align: center;
  margin-bottom: 1.5rem;
}

.reward-preview .reward-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  margin: 0 auto 1rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.reward-preview h4 {
  color: #333;
  font-family: var(--st-font-game);
  margin-bottom: 0.5rem;
}

.reward-preview p {
  color: #666;
  margin-bottom: 1rem;
}

.points-cost {
  background: linear-gradient(135deg, #FFD93D, #FF6B6B);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.confirmation-text {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.5;
}

.child-modal .modal-footer {
  padding: 1.5rem 2rem;
  border: none;
  gap: 1rem;
}

.child-modal .btn {
  border-radius: 25px;
  padding: 12px 24px;
  font-weight: bold;
  flex: 1;
}

/* ===================================
   CHILD CALENDAR PAGE STYLES
   =================================== */

/* Main Container */
.child-calendar-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 80px; /* Space for navbar */
}

/* Mobile optimizations for calendar page */
@media (max-width: 767px) {
  .child-calendar-page {
    padding-bottom: 100px; /* More space for mobile navbar */
  }
}

/* Header */
.child-calendar-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

/* Mobile header optimizations */
@media (max-width: 767px) {
  .child-calendar-header {
    padding: 0;
  }

  .child-calendar-header .container-fluid {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .child-calendar-header .py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
}

.calendar-today-btn .btn-today {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  border: none;
  border-radius: 20px;
  color: white;
  padding: 10px 16px;
  font-weight: bold;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.calendar-today-btn .btn-today:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

/* Mobile today button optimizations */
@media (max-width: 767px) {
  .calendar-today-btn .btn-today {
    padding: 8px 12px;
    font-size: 0.8rem;
    gap: 6px;
    min-height: 44px; /* Touch target */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .calendar-today-btn .btn-today:hover {
    transform: none; /* Remove hover effect on mobile */
  }
}

/* Content Area */
.child-calendar-content {
  padding: 2rem 0;
}

/* Mobile content optimizations */
@media (max-width: 767px) {
  .child-calendar-content {
    padding: 1rem 0;
  }

  .child-calendar-content .container-fluid {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

/* Month Navigation */
.month-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.nav-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #667eea;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Mobile month navigation optimizations */
@media (max-width: 767px) {
  .month-navigation {
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0 1rem;
  }

  .nav-btn {
    width: 44px;
    height: 44px;
    font-size: 1rem;
    min-height: 44px; /* Touch target */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .month-title {
    font-size: 1.4rem;
    text-align: center;
    flex: 1;
  }
}

.nav-btn:hover {
  transform: scale(1.1);
  background: white;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.month-title {
  color: white;
  font-family: var(--st-font-game);
  font-size: 1.8rem;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  text-transform: capitalize;
  margin: 0;
}

/* Calendar Container */
.calendar-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  margin-bottom: 2rem;
}

/* Mobile calendar container optimizations */
@media (max-width: 767px) {
  .calendar-container {
    border-radius: 15px;
    padding: 1rem;
    margin-bottom: 1rem;
    margin-left: -0.75rem;
    margin-right: -0.75rem;
    border-radius: 0;
    box-shadow: none;
    background: white;
  }
}

/* Weekday Headers */
.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-bottom: 1rem;
}

.weekday-header {
  text-align: center;
  font-weight: bold;
  color: #667eea;
  font-size: 0.9rem;
  padding: 8px;
}

/* Mobile weekday header optimizations */
@media (max-width: 767px) {
  .weekday-header {
    font-size: 0.8rem;
    padding: 6px 4px;
  }
}

/* Calendar Days */
.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
}

/* Mobile calendar days optimizations */
@media (max-width: 767px) {
  .calendar-days {
    gap: 4px;
  }

  .calendar-weekdays {
    gap: 4px;
    margin-bottom: 0.5rem;
  }
}

.calendar-day {
  aspect-ratio: 1;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
}

/* Mobile calendar day optimizations */
@media (max-width: 767px) {
  .calendar-day {
    border-radius: 8px;
    padding: 6px;
    min-height: 44px; /* Touch target */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .calendar-day:hover {
    transform: scale(1.02); /* Reduced hover effect for mobile */
  }
}

.calendar-day:hover {
  transform: scale(1.05);
  background: #e9ecef;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.calendar-day.other-month {
  opacity: 0.3;
  pointer-events: none;
}

.calendar-day.today {
  background: linear-gradient(135deg, #FFD93D, #FF6B6B);
  color: white;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.calendar-day.selected {
  border-color: #4ECDC4;
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}

.day-number {
  font-weight: bold;
  font-size: 0.9rem;
  margin-bottom: 4px;
}

.task-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  justify-content: center;
  margin-top: auto;
}

.task-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #4ECDC4;
}

.more-tasks {
  font-size: 0.6rem;
  color: #666;
  font-weight: bold;
}

/* Mobile task indicators optimizations */
@media (max-width: 767px) {
  .task-indicators {
    gap: 1px;
    margin-top: 4px;
  }

  .task-dot {
    width: 5px;
    height: 5px;
  }

  .more-tasks {
    font-size: 0.55rem;
  }

  .day-number {
    font-size: 0.8rem;
    margin-bottom: 2px;
  }
}

/* Selected Date Section */
.selected-date-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

/* Mobile selected date section optimizations */
@media (max-width: 767px) {
  .selected-date-section {
    border-radius: 0;
    padding: 1rem;
    margin-left: -0.75rem;
    margin-right: -0.75rem;
    box-shadow: none;
    background: white;
    margin-top: 1rem;
  }
}

.selected-date-title {
  color: #333;
  font-family: var(--st-font-game);
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  text-transform: capitalize;
}

/* No Tasks State */
.no-tasks {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.no-tasks-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-tasks h5 {
  color: #333;
  font-family: var(--st-font-game);
  margin-bottom: 0.5rem;
}

.no-tasks p {
  color: #666;
  font-size: 1rem;
}

/* Tasks List */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.task-card {
  background: white;
  border-radius: 15px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  border: 2px solid #f0f0f0;
  cursor: pointer;
}

/* Mobile task card optimizations */
@media (max-width: 767px) {
  .task-card {
    border-radius: 12px;
    padding: 0.75rem;
    gap: 0.75rem;
    min-height: 44px; /* Touch target */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .task-card:hover {
    transform: translateY(-1px); /* Reduced hover effect */
  }
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-color: #4ECDC4;
}

.task-status-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* Mobile task elements optimizations */
@media (max-width: 767px) {
  .task-status-icon {
    width: 35px;
    height: 35px;
    font-size: 1.2rem;
  }

  .task-title {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
  }

  .task-description {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
  }

  .task-meta {
    gap: 0.75rem;
    font-size: 0.75rem;
  }

  .completed-badge {
    padding: 6px 12px;
    font-size: 0.75rem;
    gap: 4px;
  }

  .start-task-btn {
    padding: 8px 16px;
    font-size: 0.8rem;
    gap: 6px;
  }
}

.task-content {
  flex: 1;
}

.task-title {
  color: #333;
  font-weight: bold;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.task-description {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #888;
}

.task-points,
.task-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

.task-action {
  flex-shrink: 0;
}

.completed-badge {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.start-task-btn {
  background: linear-gradient(135deg, #FFD93D, #FF6B6B);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.start-task-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

/* ===================================
   CHILD TROPHIES PAGE STYLES
   =================================== */

/* Main Container */
.child-trophies-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 80px; /* Space for navbar */
}

/* Header */
.child-trophies-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.trophy-progress-circle {
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: conic-gradient(
    #4ECDC4 0deg,
    #4ECDC4 calc(var(--progress) * 3.6deg),
    #e9ecef calc(var(--progress) * 3.6deg),
    #e9ecef 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Mobile optimizations for progress circle */
@media (max-width: 767px) {
  .progress-circle {
    width: 50px;
    height: 50px;
  }

  .progress-circle::before {
    width: 38px;
    height: 38px;
  }

  .progress-text {
    font-size: 0.7rem;
  }
}

.progress-circle::before {
  content: '';
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: white;
  position: absolute;
}

.progress-text {
  position: relative;
  z-index: 1;
  font-weight: bold;
  font-size: 0.8rem;
  color: #333;
}

/* Stats Section */
.child-trophies-stats {
  padding: 1rem 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

/* Mobile optimizations for stats */
@media (max-width: 767px) {
  .stats-grid {
    gap: 0.5rem;
  }
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Mobile optimizations for stat cards */
@media (max-width: 767px) {
  .stat-card {
    padding: 0.75rem;
    gap: 0.75rem;
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    width: 35px;
    height: 35px;
    font-size: 1.2rem;
  }

  .stat-number {
    font-size: 1.1rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.stat-card.achieved {
  border-left: 4px solid #4ECDC4;
}

.stat-card.pending {
  border-left: 4px solid #FFD93D;
}

.stat-card.total {
  border-left: 4px solid #FF6B6B;
}

.stat-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  font-family: var(--st-font-game);
}

.stat-label {
  font-size: 0.8rem;
  color: #666;
  font-weight: 600;
}

/* Filters Section */
.child-trophies-filters {
  padding: 1rem 0;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-tab {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  color: #666;
  font-weight: 600;
  flex-shrink: 0;
}

/* Mobile optimizations for filter tabs */
@media (max-width: 767px) {
  .filter-tabs {
    gap: 0.25rem;
    padding: 0 1rem;
  }

  .filter-tab {
    padding: 8px 16px;
    font-size: 0.8rem;
    gap: 6px;
  }

  .tab-icon {
    font-size: 0.9rem;
  }

  .tab-text {
    font-size: 0.8rem;
  }
}

.filter-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.filter-tab.active {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.tab-icon {
  font-size: 1rem;
}

.tab-text {
  font-size: 0.9rem;
}

/* Content Area */
.child-trophies-content {
  padding: 1rem 0;
}

/* Trophies Grid */
.trophies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

/* Mobile optimizations for trophies grid */
@media (max-width: 767px) {
  .trophies-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Trophy Card */
.trophy-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

/* Mobile optimizations for trophy cards */
@media (max-width: 767px) {
  .trophy-card {
    padding: 1rem;
    border-radius: 15px;
  }

  .trophy-card:hover {
    transform: translateY(-2px) scale(1.01);
  }
}

.trophy-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.trophy-card.locked {
  opacity: 0.7;
  filter: grayscale(0.3);
}

.trophy-card.achieved {
  border-color: #4ECDC4;
  background: linear-gradient(145deg, #fff 0%, #f8fff8 100%);
}

/* Trophy Header */
.trophy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.trophy-rarity-badge {
  background: #96CEB4;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.7rem;
  text-transform: uppercase;
}

.achievement-badge {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

/* Trophy Icon */
.trophy-icon-container {
  text-align: center;
  margin-bottom: 1rem;
  position: relative;
}

.trophy-icon {
  font-size: 3rem;
  transition: all 0.3s ease;
  display: inline-block;
}

/* Mobile optimizations for trophy icons */
@media (max-width: 767px) {
  .trophy-icon-container {
    margin-bottom: 0.75rem;
  }

  .trophy-icon {
    font-size: 2.5rem;
  }
}

.trophy-icon.achieved {
  animation: trophyGlow 2s ease-in-out infinite alternate;
}

.trophy-icon.locked {
  filter: grayscale(100%);
  opacity: 0.5;
}

.shine-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
  animation: shine 3s ease-in-out infinite;
}

@keyframes trophyGlow {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Trophy Content */
.trophy-content {
  text-align: center;
}

.trophy-title {
  color: #333;
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  font-family: var(--st-font-game);
}

.trophy-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
}

/* Mobile optimizations for trophy content */
@media (max-width: 767px) {
  .trophy-title {
    font-size: 1rem;
    margin-bottom: 0.25rem;
  }

  .trophy-description {
    font-size: 0.85rem;
    margin-bottom: 0.75rem;
    line-height: 1.3;
  }
}

/* Trophy Achievement Info */
.trophy-achieved {
  background: #f8fff8;
  border-radius: 10px;
  padding: 1rem;
  border: 1px solid #4ECDC4;
}

.achieved-date {
  color: #4ECDC4;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.trophy-points {
  color: #FFD93D;
  font-weight: bold;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* Trophy Progress */
.trophy-progress {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.requirement-progress {
  margin-bottom: 1rem;
}

.progress-bar-container {
  margin-bottom: 0.5rem;
}

.progress-label {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.progress-bar {
  background: #e9ecef;
  border-radius: 10px;
  height: 8px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.progress-fill {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.7rem;
  color: #888;
  text-align: right;
  font-weight: 600;
}

/* Mobile touch optimizations */
@media (max-width: 767px) {
  .trophy-card {
    min-height: 44px; /* Minimum touch target size */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .filter-tab {
    min-height: 44px; /* Minimum touch target size */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .category-btn {
    min-height: 44px; /* Minimum touch target size */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Improve spacing for mobile */
  .child-trophies-content {
    padding: 0.5rem 0 2rem 0;
  }

  .child-trophies-filters {
    padding: 0.5rem 0;
  }

  .child-trophies-stats {
    padding: 0.5rem 0;
  }

  /* Better mobile typography */
  .trophy-rarity-badge {
    font-size: 0.6rem;
    padding: 3px 8px;
  }

  .achievement-badge {
    width: 25px;
    height: 25px;
    font-size: 0.7rem;
  }

  /* Progress bars mobile optimization */
  .progress-bar {
    height: 6px;
  }

  .progress-label {
    font-size: 0.75rem;
  }

  .progress-text {
    font-size: 0.65rem;
  }

  /* Trophy achievement info mobile */
  .trophy-achieved,
  .trophy-progress {
    padding: 0.75rem;
    border-radius: 8px;
  }

  .achieved-date {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
  }

  .trophy-points {
    font-size: 0.8rem;
  }
}

/* ===================================
   CHILD THEMES PAGE STYLES
   =================================== */

/* Main Container */
.child-themes-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 80px; /* Space for navbar */
}

/* Header */
.child-themes-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

/* Content Area */
.child-themes-content {
  padding: 2rem 0;
}

/* Themes Grid */
.themes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Theme Card */
.theme-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 3px solid transparent;
  backdrop-filter: blur(10px);
  position: relative;
}

.theme-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.theme-card.active {
  border-color: #4ECDC4;
  background: linear-gradient(145deg, #fff 0%, #f0fff4 100%);
}

.theme-card.locked {
  opacity: 0.6;
  filter: grayscale(0.5);
  cursor: not-allowed;
}

.theme-card.locked:hover {
  transform: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Theme Image */
.theme-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.theme-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.theme-card:hover .theme-image {
  transform: scale(1.05);
}

/* Status Overlays */
.theme-status-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  font-size: 1rem;
}

.theme-status-overlay.active {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
}

.theme-status-overlay.locked {
  background: rgba(0, 0, 0, 0.7);
}

/* Rarity Badge */
.theme-rarity-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #96CEB4;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.7rem;
  text-transform: uppercase;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Theme Content */
.theme-content {
  padding: 1.5rem;
}

.theme-title {
  color: #333;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  font-family: var(--st-font-game);
}

.theme-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
}

/* Theme Footer */
.theme-footer {
  margin-top: auto;
}

.theme-active-badge {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: bold;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.theme-activate-btn {
  background: linear-gradient(135deg, #FFD93D, #FF6B6B);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: bold;
  width: 100%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.theme-activate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.theme-price {
  background: linear-gradient(135deg, #FFD93D, #FF6B6B);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: bold;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.theme-locked {
  background: #f8f9fa;
  color: #6c757d;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 600;
  text-align: center;
  border: 2px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 0.9rem;
}

/* Modal Styles */
.child-themes-page .modal-content {
  border-radius: 20px;
  border: none;
  overflow: hidden;
}

.child-themes-page .modal-header {
  border: none;
  padding: 1.5rem;
}

.child-themes-page .modal-title {
  font-family: var(--st-font-game);
  font-size: 1.3rem;
  font-weight: bold;
}

.child-themes-page .modal-body {
  padding: 2rem;
}

.theme-details {
  margin-top: 1.5rem;
}

.detail-item {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.rarity-badge {
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.price-badge {
  background: linear-gradient(135deg, #FFD93D, #FF6B6B);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 6px;
}

.child-themes-page .modal-footer {
  padding: 1.5rem 2rem;
  border: none;
  gap: 1rem;
}

.child-themes-page .btn {
  border-radius: 25px;
  padding: 12px 24px;
  font-weight: bold;
  flex: 1;
}

/* Mobile optimizations for themes */
@media (max-width: 767px) {
  .child-themes-page {
    padding-bottom: 100px; /* More space for mobile navbar */
  }

  .child-themes-content {
    padding: 1rem 0;
  }

  .themes-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .theme-card {
    border-radius: 15px;
  }

  .theme-card:hover {
    transform: translateY(-2px) scale(1.01);
  }

  .theme-image-container {
    height: 180px;
  }

  .theme-content {
    padding: 1rem;
  }

  .theme-title {
    font-size: 1.1rem;
  }

  .theme-description {
    font-size: 0.85rem;
    margin-bottom: 0.75rem;
  }

  .theme-activate-btn,
  .theme-price,
  .theme-active-badge,
  .theme-locked {
    padding: 8px 16px;
    font-size: 0.85rem;
  }

  .theme-status-overlay {
    padding: 8px 16px;
    font-size: 0.9rem;
  }

  .theme-rarity-badge {
    top: 8px;
    right: 8px;
    padding: 3px 8px;
    font-size: 0.65rem;
  }

  /* Modal mobile optimizations */
  .child-themes-page .modal-dialog {
    margin: 1rem;
  }

  .child-themes-page .modal-body {
    padding: 1rem;
  }

  .child-themes-page .modal-body .row {
    flex-direction: column;
  }

  .child-themes-page .modal-body .col-md-6 {
    margin-bottom: 1rem;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* ===================================
   AUTHENTICATION PAGES STYLES
   =================================== */

/* Main containers */
.adult-login-page,
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Branding section */
.login-branding {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.branding-content {
  max-width: 500px;
  text-align: center;
}

.brand-logo {
  margin-bottom: 3rem;
}

.brand-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  font-family: var(--st-font-game);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 0;
}

.brand-features {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  text-align: left;
}

.feature-icon {
  font-size: 2.5rem;
  color: #FFD93D;
  width: 60px;
  flex-shrink: 0;
}

.feature-item h4 {
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.feature-item p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 0.95rem;
}

/* Form section */
.login-form-section {
  background: white;
  padding: 2rem;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
}

.login-title {
  color: #333;
  font-size: 2rem;
  font-weight: bold;
  font-family: var(--st-font-game);
}

.login-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.login-form {
  margin-top: 2rem;
}

.login-form .form-label {
  color: #333;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.login-form .form-control {
  border-radius: 10px;
  border: 2px solid #e9ecef;
  padding: 12px 16px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.login-form .form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.login-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 10px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.login-btn:disabled {
  opacity: 0.7;
  transform: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.forgot-password-link {
  color: #667eea;
  text-decoration: none;
  font-size: 0.9rem;
}

.forgot-password-link:hover {
  color: #5a67d8;
  text-decoration: underline;
}

.register-link {
  color: #666;
  margin: 0;
}

.register-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.register-link a:hover {
  color: #5a67d8;
  text-decoration: underline;
}

.back-link,
.child-access-link {
  color: #667eea;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
}

.back-link:hover,
.child-access-link:hover {
  color: #5a67d8;
  text-decoration: underline;
}

/* Mobile optimizations */
@media (max-width: 991px) {
  .login-branding {
    display: none;
  }

  .login-form-section {
    min-height: 100vh;
  }

  .brand-title {
    font-size: 2.5rem;
  }

  .feature-item {
    gap: 1rem;
  }

  .feature-icon {
    font-size: 2rem;
    width: 50px;
  }
}

/* ===================================
   CHILD iMESSAGE ANIMATIONS
   =================================== */

@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes typingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes sendPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8) rotate(0deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2) rotate(180deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

@media (max-width: 768px) {
  .child-imessage-conversation {
    height: 100vh;
  }

  .child-imessage-header {
    padding: 0.75rem 0;
  }

  .child-imessage-chat-title {
    font-size: 1.1rem;
  }

  .child-imessage-messages-container {
    padding: 0.75rem;
    padding-bottom: 140px;
  }

  .child-imessage-bubble-wrapper {
    max-width: 90%;
  }

  .child-imessage-bubble {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
  }

  .child-imessage-avatar {
    width: 36px;
    height: 36px;
  }

  .child-imessage-avatar-emoji {
    font-size: 1rem;
  }

  .child-imessage-input-container {
    padding: 0.75rem;
  }

  .child-imessage-quick-emojis {
    margin-bottom: 0.5rem;
  }

  .child-imessage-quick-emoji-btn {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .child-imessage-input-box {
    padding: 0.5rem;
  }

  .child-imessage-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .child-imessage-send-btn,
  .child-imessage-emoji-btn {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }

  .child-imessage-back-btn,
  .child-imessage-call-btn {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .child-imessage-bubble-wrapper {
    max-width: 95%;
  }

  .child-imessage-bubble {
    padding: 0.75rem 0.875rem;
    font-size: 0.9rem;
  }

  .child-imessage-input-container {
    padding: 0.5rem;
  }

  .child-imessage-quick-emoji-btn {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }
}

/* ===================================
   ACCESSIBILITY IMPROVEMENTS
   =================================== */

/* Focus states */
.child-imessage-back-btn:focus,
.child-imessage-call-btn:focus,
.child-imessage-send-btn:focus,
.child-imessage-emoji-btn:focus,
.child-imessage-quick-emoji-btn:focus {
  outline: 3px solid rgba(255, 107, 107, 0.5);
  outline-offset: 2px;
}

.child-imessage-input:focus {
  outline: none;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .child-imessage-bubble {
    border: 3px solid currentColor;
  }

  .child-imessage-input-box {
    border: 3px solid currentColor;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .child-imessage-bubble,
  .child-imessage-back-btn,
  .child-imessage-call-btn,
  .child-imessage-send-btn,
  .child-imessage-emoji-btn,
  .child-imessage-quick-emoji-btn {
    animation: none;
    transition: none;
  }

  .child-imessage-sparkle {
    display: none;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .conversation-messages {
    scroll-behavior: auto;
  }
}