import { Timestamp } from 'firebase/firestore';

// Chat message types
export interface ChatMessage {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  type: 'text' | 'image' | 'file';
  timestamp: Timestamp;
  status: 'sent' | 'delivered' | 'read';
  replyTo?: string; // ID of message being replied to
  edited?: boolean;
  editedAt?: Timestamp;
}

// Chat room types
export interface ChatRoom {
  id: string;
  name: string;
  type: 'family' | 'private' | 'group';
  participants: string[]; // User IDs
  participantDetails: ChatParticipant[];
  lastMessage?: {
    content: string;
    senderId: string;
    timestamp: Timestamp;
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string;
  isActive: boolean;
  unreadCounts: { [userId: string]: number };
}

// Chat participant
export interface ChatParticipant {
  id: string;
  name: string;
  avatar?: string;
  role: 'adult' | 'child';
  isOnline: boolean;
  lastSeen?: Timestamp;
}

// Typing indicator
export interface TypingIndicator {
  chatId: string;
  userId: string;
  userName: string;
  timestamp: Timestamp;
}

// Chat context types
export interface ChatContextType {
  currentChatId: string | null;
  chatRooms: ChatRoom[];
  messages: { [chatId: string]: ChatMessage[] };
  typingUsers: { [chatId: string]: TypingIndicator[] };
  loading: boolean;
  
  // Chat room methods
  createChatRoom: (name: string, type: ChatRoom['type'], participants: string[]) => Promise<string>;
  joinChatRoom: (chatId: string) => Promise<void>;
  leaveChatRoom: (chatId: string) => Promise<void>;
  
  // Message methods
  sendMessage: (chatId: string, content: string, type?: ChatMessage['type']) => Promise<void>;
  editMessage: (messageId: string, newContent: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  markAsRead: (chatId: string, messageId: string) => Promise<void>;
  
  // Typing indicators
  startTyping: (chatId: string) => void;
  stopTyping: (chatId: string) => void;
  
  // Real-time subscriptions
  subscribeToChat: (chatId: string) => () => void;
  subscribeToUserChats: () => () => void;
}
