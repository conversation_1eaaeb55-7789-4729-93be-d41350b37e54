import { Reward, RewardTemplate, RewardCategory, RewardStats, Child } from '../types/schema';
import { RewardStatus, RewardType } from '../types/enums';
import { mockChildren } from './TaskMockData';

// Mock reward categories
export const mockRewardCategories: RewardCategory[] = [
  {
    id: 'reward-cat-1',
    name: 'Experiência<PERSON>',
    color: '#FF6B6B',
    icon: '🎪'
  },
  {
    id: 'reward-cat-2',
    name: 'Privilégio<PERSON>',
    color: '#4ECDC4',
    icon: '👑'
  },
  {
    id: 'reward-cat-3',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    color: '#45B7D1',
    icon: '🧸'
  },
  {
    id: 'reward-cat-4',
    name: 'Tecnologia',
    color: '#96CEB4',
    icon: '📱'
  },
  {
    id: 'reward-cat-5',
    name: 'Doces',
    color: '#FECA57',
    icon: '🍭'
  },
  {
    id: 'reward-cat-6',
    name: 'Atividades',
    color: '#FF9FF3',
    icon: '🎨'
  }
];

// Mock reward templates
export const mockRewardTemplates: RewardTemplate[] = [
  {
    id: 'reward-template-1',
    title: 'Hora extra de ecrã',
    description: '15 minutos adicionais de tempo de ecrã',
    category: mockRewardCategories[3], // Tecnologia
    pointsRequired: 20,
    type: RewardType.PRIVILEGE,
    instructions: 'Pode usar o tablet/telemóvel por mais 15 minutos hoje',
    tags: ['tecnologia', 'tempo', 'ecrã'],
    isSystemTemplate: true
  },
  {
    id: 'reward-template-2',
    title: 'Escolher o jantar',
    description: 'Escolher o que a família vai jantar',
    category: mockRewardCategories[1], // Privilégios
    pointsRequired: 30,
    type: RewardType.PRIVILEGE,
    instructions: 'Pode escolher o menu do jantar de hoje',
    tags: ['comida', 'escolha', 'família'],
    isSystemTemplate: true
  },
  {
    id: 'reward-template-3',
    title: 'Brinquedo pequeno',
    description: 'Um brinquedo pequeno da loja',
    category: mockRewardCategories[2], // Brinquedos
    pointsRequired: 50,
    type: RewardType.MATERIAL,
    instructions: 'Pode escolher um brinquedo pequeno até 10€',
    tags: ['brinquedo', 'loja', 'presente'],
    isSystemTemplate: true
  },
  {
    id: 'reward-template-4',
    title: 'Dormir mais tarde',
    description: 'Ficar acordado 30 minutos mais tarde',
    category: mockRewardCategories[1], // Privilégios
    pointsRequired: 25,
    type: RewardType.PRIVILEGE,
    instructions: 'Pode ficar acordado até 30 minutos mais tarde',
    tags: ['sono', 'hora', 'privilégio'],
    isSystemTemplate: true
  },
  {
    id: 'reward-template-5',
    title: 'Sobremesa especial',
    description: 'Uma sobremesa à escolha',
    category: mockRewardCategories[4], // Doces
    pointsRequired: 15,
    type: RewardType.INSTANT,
    instructions: 'Pode escolher uma sobremesa especial para hoje',
    tags: ['doce', 'sobremesa', 'comida'],
    isSystemTemplate: true
  },
  {
    id: 'reward-template-6',
    title: 'Sessão de cinema em casa',
    description: 'Escolher um filme para ver em família',
    category: mockRewardCategories[0], // Experiências
    pointsRequired: 40,
    type: RewardType.EXPERIENCE,
    instructions: 'Pode escolher o filme para a sessão de cinema em família',
    tags: ['filme', 'família', 'cinema'],
    isSystemTemplate: true
  },
  {
    id: 'reward-template-7',
    title: 'Passeio ao parque',
    description: 'Ir ao parque preferido',
    category: mockRewardCategories[5], // Atividades
    pointsRequired: 35,
    type: RewardType.EXPERIENCE,
    instructions: 'Pode escolher ir ao parque preferido',
    tags: ['parque', 'ar livre', 'diversão'],
    isSystemTemplate: true
  },
  {
    id: 'reward-template-8',
    title: 'Kit de arte',
    description: 'Kit de materiais de arte',
    category: mockRewardCategories[5], // Atividades
    pointsRequired: 60,
    type: RewardType.MATERIAL,
    instructions: 'Recebe um kit de materiais de arte para criar',
    tags: ['arte', 'criatividade', 'materiais'],
    isSystemTemplate: true
  }
];

// Mock rewards
export const mockRewards: Reward[] = [
  {
    id: 'reward-1',
    title: 'Hora extra de ecrã',
    description: '15 minutos adicionais de tempo de ecrã',
    childId: 'child-1',
    childName: 'João',
    pointsRequired: 20,
    category: mockRewardCategories[3], // Tecnologia
    status: RewardStatus.ACTIVE,
    type: RewardType.PRIVILEGE,
    createdAt: new Date('2024-01-19T10:00:00'),
    updatedAt: new Date('2024-01-19T10:00:00'),
    expiresAt: new Date('2024-01-26T23:59:59'),
    instructions: 'Pode usar o tablet/telemóvel por mais 15 minutos hoje',
    tags: ['tecnologia', 'tempo', 'ecrã']
  },
  {
    id: 'reward-2',
    title: 'Escolher o jantar',
    description: 'Escolher o que a família vai jantar',
    childId: 'child-2',
    childName: 'Maria',
    pointsRequired: 30,
    category: mockRewardCategories[1], // Privilégios
    status: RewardStatus.CLAIMED,
    type: RewardType.PRIVILEGE,
    createdAt: new Date('2024-01-18T14:00:00'),
    updatedAt: new Date('2024-01-19T18:30:00'),
    claimedAt: new Date('2024-01-19T18:30:00'),
    instructions: 'Pode escolher o menu do jantar de hoje',
    tags: ['comida', 'escolha', 'família']
  },
  {
    id: 'reward-3',
    title: 'Brinquedo pequeno',
    description: 'Um brinquedo pequeno da loja',
    childId: 'child-3',
    childName: 'Pedro',
    pointsRequired: 50,
    category: mockRewardCategories[2], // Brinquedos
    status: RewardStatus.ACTIVE,
    type: RewardType.MATERIAL,
    createdAt: new Date('2024-01-17T09:00:00'),
    updatedAt: new Date('2024-01-17T09:00:00'),
    expiresAt: new Date('2024-02-17T23:59:59'),
    instructions: 'Pode escolher um brinquedo pequeno até 10€',
    tags: ['brinquedo', 'loja', 'presente']
  },
  {
    id: 'reward-4',
    title: 'Dormir mais tarde',
    description: 'Ficar acordado 30 minutos mais tarde',
    childId: 'child-1',
    childName: 'João',
    pointsRequired: 25,
    category: mockRewardCategories[1], // Privilégios
    status: RewardStatus.EXPIRED,
    type: RewardType.PRIVILEGE,
    createdAt: new Date('2024-01-15T16:00:00'),
    updatedAt: new Date('2024-01-16T16:00:00'),
    expiresAt: new Date('2024-01-16T23:59:59'),
    instructions: 'Pode ficar acordado até 30 minutos mais tarde',
    tags: ['sono', 'hora', 'privilégio']
  },
  {
    id: 'reward-5',
    title: 'Sobremesa especial',
    description: 'Uma sobremesa à escolha',
    childId: 'child-2',
    childName: 'Maria',
    pointsRequired: 15,
    category: mockRewardCategories[4], // Doces
    status: RewardStatus.ACTIVE,
    type: RewardType.INSTANT,
    createdAt: new Date('2024-01-19T12:00:00'),
    updatedAt: new Date('2024-01-19T12:00:00'),
    expiresAt: new Date('2024-01-20T23:59:59'),
    instructions: 'Pode escolher uma sobremesa especial para hoje',
    tags: ['doce', 'sobremesa', 'comida']
  },
  {
    id: 'reward-6',
    title: 'Sessão de cinema em casa',
    description: 'Escolher um filme para ver em família',
    childId: 'child-3',
    childName: 'Pedro',
    pointsRequired: 40,
    category: mockRewardCategories[0], // Experiências
    status: RewardStatus.ACTIVE,
    type: RewardType.EXPERIENCE,
    createdAt: new Date('2024-01-18T11:00:00'),
    updatedAt: new Date('2024-01-18T11:00:00'),
    expiresAt: new Date('2024-01-25T23:59:59'),
    instructions: 'Pode escolher o filme para a sessão de cinema em família',
    tags: ['filme', 'família', 'cinema']
  },
  {
    id: 'reward-7',
    title: 'Passeio ao parque',
    description: 'Ir ao parque preferido',
    childId: 'child-1',
    childName: 'João',
    pointsRequired: 35,
    category: mockRewardCategories[5], // Atividades
    status: RewardStatus.CLAIMED,
    type: RewardType.EXPERIENCE,
    createdAt: new Date('2024-01-16T10:00:00'),
    updatedAt: new Date('2024-01-17T15:00:00'),
    claimedAt: new Date('2024-01-17T15:00:00'),
    instructions: 'Pode escolher ir ao parque preferido',
    tags: ['parque', 'ar livre', 'diversão']
  },
  {
    id: 'reward-8',
    title: 'Kit de arte',
    description: 'Kit de materiais de arte',
    childId: 'child-2',
    childName: 'Maria',
    pointsRequired: 60,
    category: mockRewardCategories[5], // Atividades
    status: RewardStatus.ACTIVE,
    type: RewardType.MATERIAL,
    createdAt: new Date('2024-01-14T13:00:00'),
    updatedAt: new Date('2024-01-14T13:00:00'),
    expiresAt: new Date('2024-02-14T23:59:59'),
    instructions: 'Recebe um kit de materiais de arte para criar',
    tags: ['arte', 'criatividade', 'materiais']
  },
  {
    id: 'reward-9',
    title: 'Hora extra de tablet',
    description: '30 minutos adicionais de tempo de ecrã',
    childId: 'child-123',
    childName: 'João',
    pointsRequired: 25,
    category: mockRewardCategories[3], // Tecnologia
    status: RewardStatus.CLAIMED,
    type: RewardType.PRIVILEGE,
    createdAt: new Date('2024-01-19T10:00:00'),
    updatedAt: new Date('2024-01-19T16:30:00'),
    // claimedAt: undefined, // Pendente de aprovação
    instructions: 'Pode usar o tablet por mais 30 minutos hoje',
    tags: ['tecnologia', 'tempo', 'ecrã']
  }
];

// Mock reward statistics
export const mockRewardStats: RewardStats = {
  totalRewards: 9,
  activeRewards: 5,
  claimedRewards: 3,
  expiredRewards: 1,
  totalPointsSpent: 200,
  averagePointsPerReward: 33.33,
  weeklyTrend: [1, 2, 0, 1, 1, 2, 2] // Rewards claimed each day this week
};
