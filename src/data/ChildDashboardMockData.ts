import { ChildDashboardSection, NavCardType } from '../types/enums';
import { ChildDashboardStats, ChildNavigationCard, ChildUser } from '../types/schema';

// Mock Child User
export const mockChildUser: ChildUser = {
  id: 'child-123',
  name: '<PERSON>',
  age: 8,
  avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  familyName: 'F<PERSON><PERSON><PERSON>',
  totalPoints: 1250,
  level: 3,
  currentTheme: 'spiderman-theme'
};

// Mock Child Dashboard Stats
export const mockChildDashboardStats: ChildDashboardStats = {
  totalTasks: 15,
  completedTasks: 12,
  pendingTasks: 3,
  totalPoints: 1250,
  availableRewards: 5,
  achievedTrophies: 8,
  weeklyProgress: 80,
  streak: 7
};

// Mock Child Navigation Cards
export const mockChildNavigationCards: ChildNavigationCard[] = [
  {
    id: '1',
    title: 'As <PERSON><PERSON>',
    description: 'Ver e completar tarefas',
    icon: '📝',
    section: ChildDashboardSection.MY_TASKS,
    type: NavCardType.PRIMARY,
    count: 3,
    route: '/child-tasks',
    color: '#FF6B35'
  },
  {
    id: '2',
    title: 'Os Meus Troféus',
    description: 'Ver conquistas e medalhas',
    icon: '🏆',
    section: ChildDashboardSection.MY_TROPHIES,
    type: NavCardType.ACCENT,
    count: 8,
    route: '/child-trophies',
    color: '#FFD700'
  },
  {
    id: '3',
    title: 'As Minhas Recompensas',
    description: 'Ver recompensas disponíveis',
    icon: '🎁',
    section: ChildDashboardSection.MY_REWARDS,
    type: NavCardType.SECONDARY,
    count: 5,
    route: '/child-rewards',
    color: '#32CD32'
  },
  {
    id: '4',
    title: 'Os Meus Temas',
    description: 'Personalizar a aplicação',
    icon: '🎨',
    section: ChildDashboardSection.MY_THEMES,
    type: NavCardType.SECONDARY,
    count: 4,
    route: '/child-themes',
    color: '#9370DB'
  },
  {
    id: '5',
    title: 'Chat da Família',
    description: 'Falar com a família',
    icon: '💬',
    section: ChildDashboardSection.FAMILY_CHAT,
    type: NavCardType.PRIMARY,
    count: 2,
    route: '/child-chat-adult-style',
    color: '#4169E1'
  },
  {
    id: '6',
    title: 'Definições',
    description: 'Configurar perfil',
    icon: '⚙️',
    section: ChildDashboardSection.SETTINGS,
    type: NavCardType.SECONDARY,
    count: 0,
    route: '/child-settings',
    color: '#6B7280'
  }
];

// Mock Recent Activities
export const mockRecentActivities = [
  {
    id: '1',
    type: 'task_completed',
    title: 'Arrumaste o quarto!',
    description: 'Ganhaste 50 pontos',
    points: 50,
    timestamp: new Date('2024-01-21T10:30:00'),
    icon: '✨'
  },
  {
    id: '2',
    type: 'trophy_earned',
    title: 'Troféu "Organizado" desbloqueado!',
    description: 'Completaste 10 tarefas de organização',
    points: 100,
    timestamp: new Date('2024-01-21T09:15:00'),
    icon: '🏆'
  },
  {
    id: '3',
    type: 'reward_claimed',
    title: 'Recompensa "Tempo de Jogo" reclamada!',
    description: '30 minutos de tempo extra',
    points: -200,
    timestamp: new Date('2024-01-20T16:45:00'),
    icon: '🎮'
  },
  {
    id: '4',
    type: 'streak_achieved',
    title: 'Sequência de 7 dias!',
    description: 'Parabéns pela consistência',
    points: 75,
    timestamp: new Date('2024-01-20T08:00:00'),
    icon: '🔥'
  }
];

// Mock Level Progress
export const mockLevelProgress = {
  currentLevel: 3,
  currentXP: 750,
  nextLevelXP: 1000,
  levelName: 'Aventureiro',
  levelIcon: '🌟',
  nextLevelName: 'Explorador',
  progressPercentage: 75
};

// Mock Weekly Goals
export const mockWeeklyGoals = [
  {
    id: '1',
    title: 'Completar 5 tarefas',
    progress: 4,
    target: 5,
    completed: false,
    reward: 'Tempo extra de jogo'
  },
  {
    id: '2',
    title: 'Manter sequência de 7 dias',
    progress: 7,
    target: 7,
    completed: true,
    reward: 'Pontos bónus'
  },
  {
    id: '3',
    title: 'Arrumar o quarto 3 vezes',
    progress: 2,
    target: 3,
    completed: false,
    reward: 'Escolher filme da família'
  }
];

// Mock Available Themes
export const mockAvailableThemes = [
  {
    id: 'spiderman-theme',
    name: 'Spider-Man',
    icon: '🕷️',
    isEquipped: true,
    isAvailable: true
  },
  {
    id: 'ariel-theme',
    name: 'Pequena Sereia',
    icon: '🧜‍♀️',
    isEquipped: false,
    isAvailable: true
  },
  {
    id: 'batman-theme',
    name: 'Batman',
    icon: '🦇',
    isEquipped: false,
    isAvailable: false
  },
  {
    id: 'cinderella-theme',
    name: 'Cinderela',
    icon: '👸',
    isEquipped: false,
    isAvailable: true
  }
];
