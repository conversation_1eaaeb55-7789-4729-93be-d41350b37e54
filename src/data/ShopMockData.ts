import { ShopItem, ShopCategory, ShopStats, UserBalance, ShopPurchase } from '../types/schema';
import { ShopItemCategory, ShopItemType, ShopItemRarity, ShopItemStatus, PaymentMethod } from '../types/enums';

// Mock Shop Categories
export const mockShopCategories: ShopCategory[] = [
  {
    id: 'superhero',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    icon: '🦸‍♂️',
    description: 'Temas de super-heróis famosos',
    color: '#FF6B35',
    itemCount: 12
  },
  {
    id: 'princess',
    name: '<PERSON><PERSON>',
    icon: '👸',
    description: 'Temas de princesas encantadas',
    color: '#FF69B4',
    itemCount: 8
  },
  {
    id: 'animal',
    name: 'Anima<PERSON>',
    icon: '🐾',
    description: 'Temas com animais adoráveis',
    color: '#32CD32',
    itemCount: 6
  },
  {
    id: 'space',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    icon: '🚀',
    description: 'Aventuras espaciais',
    color: '#4169E1',
    itemCount: 5
  },
  {
    id: 'fantasy',
    name: '<PERSON><PERSON><PERSON>',
    icon: '🧙‍♂️',
    description: 'Mundos mágicos e encantados',
    color: '#9370DB',
    itemCount: 7
  },
  {
    id: 'sports',
    name: 'Desportos',
    icon: '⚽',
    description: 'Temas desportivos',
    color: '#FFD700',
    itemCount: 4
  }
];

// Mock Shop Items
export const mockShopItems: ShopItem[] = [
  // Super-Heróis
  {
    id: 'spiderman-theme',
    name: 'Tema Spider-Man',
    description: 'Personaliza a tua aplicação com o incrível Spider-Man! Inclui fundos, ícones e sons temáticos.',
    category: ShopItemCategory.SUPERHERO,
    type: ShopItemType.THEME,
    rarity: ShopItemRarity.EPIC,
    status: ShopItemStatus.AVAILABLE,
    pricePoints: 1500,
    priceMoney: 2.99,
    imageUrl: 'https://images.unsplash.com/photo-1635805737707-575885ab0820?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1635805737707-575885ab0820?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1634017839464-5c339ebe3cb4?w=800&h=600&fit=crop'
    ],
    tags: ['spider-man', 'super-herói', 'cidade', 'teia'],
    isNew: true,
    isPopular: true,
    purchasedBy: undefined,
    availableForChildren: false,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'batman-theme',
    name: 'Tema Batman',
    description: 'A escuridão protege Gotham! Tema completo com fundos noturnos e sons de morcego.',
    category: ShopItemCategory.SUPERHERO,
    type: ShopItemType.THEME,
    rarity: ShopItemRarity.LEGENDARY,
    status: ShopItemStatus.AVAILABLE,
    pricePoints: 2500,
    priceMoney: 4.99,
    imageUrl: 'https://images.unsplash.com/photo-1608889476561-6242cfdbf622?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1608889476561-6242cfdbf622?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop'
    ],
    tags: ['batman', 'gotham', 'noite', 'morcego'],
    isPopular: true,
    isLimited: true,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10')
  },
  {
    id: 'wonder-woman-theme',
    name: 'Tema Wonder Woman',
    description: 'Força e coragem! Tema inspirado na guerreira amazona com cores douradas.',
    category: ShopItemCategory.SUPERHERO,
    type: ShopItemType.THEME,
    rarity: ShopItemRarity.EPIC,
    status: ShopItemStatus.PURCHASED,
    pricePoints: 1800,
    priceMoney: 3.49,
    imageUrl: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop'
    ],
    tags: ['wonder-woman', 'amazona', 'dourado', 'coragem'],
    purchasedAt: new Date('2024-01-20'),
    purchasedBy: 'user-123',
    availableForChildren: true,
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-20')
  },
  // Princesas
  {
    id: 'cinderella-theme',
    name: 'Tema Cinderela',
    description: 'Sonhos mágicos com a Cinderela! Fundos de castelo e sons de fada madrinha.',
    category: ShopItemCategory.PRINCESS,
    type: ShopItemType.THEME,
    rarity: ShopItemRarity.RARE,
    status: ShopItemStatus.AVAILABLE,
    pricePoints: 1200,
    priceMoney: 2.49,
    imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop'
    ],
    tags: ['cinderela', 'castelo', 'magia', 'sapatinho'],
    isNew: true,
    createdAt: new Date('2024-01-18'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: 'ariel-theme',
    name: 'Tema Pequena Sereia',
    description: 'Aventure-te no fundo do mar com a Ariel! Tema aquático com sons do oceano.',
    category: ShopItemCategory.PRINCESS,
    type: ShopItemType.THEME,
    rarity: ShopItemRarity.RARE,
    status: ShopItemStatus.EQUIPPED,
    pricePoints: 1400,
    priceMoney: 2.99,
    imageUrl: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop'
    ],
    tags: ['ariel', 'sereia', 'oceano', 'mar'],
    isPopular: true,
    purchasedAt: new Date('2024-01-05'),
    equippedAt: new Date('2024-01-05'),
    purchasedBy: 'user-123',
    availableForChildren: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-05')
  },
  {
    id: 'elsa-theme',
    name: 'Tema Frozen',
    description: 'Deixa-o ir! Tema gelado com Elsa e Anna, inclui efeitos de neve.',
    category: ShopItemCategory.PRINCESS,
    type: ShopItemType.THEME,
    rarity: ShopItemRarity.EPIC,
    status: ShopItemStatus.AVAILABLE,
    pricePoints: 2000,
    priceMoney: 4.99,
    imageUrl: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop'
    ],
    tags: ['frozen', 'elsa', 'anna', 'gelo', 'neve'],
    isLimited: true,
    purchasedBy: undefined,
    availableForChildren: false,
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-08')
  },
  // Fundos
  {
    id: 'superhero-background',
    name: 'Fundo Cidade Super-Heróis',
    description: 'Fundo épico de cidade com super-heróis voando pelos céus.',
    category: ShopItemCategory.SUPERHERO,
    type: ShopItemType.BACKGROUND,
    rarity: ShopItemRarity.COMMON,
    status: ShopItemStatus.AVAILABLE,
    pricePoints: 500,
    priceMoney: 1.99,
    imageUrl: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop'
    ],
    tags: ['cidade', 'céu', 'edifícios'],
    purchasedBy: undefined,
    availableForChildren: false,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05')
  },
  {
    id: 'princess-castle-bg',
    name: 'Fundo Castelo Princesa',
    description: 'Castelo mágico num pôr do sol dourado.',
    category: ShopItemCategory.PRINCESS,
    type: ShopItemType.BACKGROUND,
    rarity: ShopItemRarity.COMMON,
    status: ShopItemStatus.PURCHASED,
    pricePoints: 400,
    priceMoney: 1.49,
    imageUrl: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop'
    ],
    tags: ['castelo', 'pôr-do-sol', 'dourado'],
    purchasedAt: new Date('2024-01-15'),
    purchasedBy: 'user-123',
    availableForChildren: true,
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-15')
  },
  // Headers
  {
    id: 'spiderman-header',
    name: 'Header Spider-Man',
    description: 'Cabeçalho personalizado com o Spider-Man.',
    category: ShopItemCategory.SUPERHERO,
    type: ShopItemType.HEADER,
    rarity: ShopItemRarity.RARE,
    status: ShopItemStatus.AVAILABLE,
    pricePoints: 800,
    priceMoney: 1.99,
    imageUrl: 'https://images.unsplash.com/photo-1635805737707-575885ab0820?w=400&h=200&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1635805737707-575885ab0820?w=800&h=400&fit=crop'
    ],
    tags: ['spider-man', 'cabeçalho', 'vermelho'],
    purchasedBy: undefined,
    availableForChildren: false,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10')
  },
  {
    id: 'princess-header',
    name: 'Header Princesas',
    description: 'Cabeçalho elegante com tema de princesas.',
    category: ShopItemCategory.PRINCESS,
    type: ShopItemType.HEADER,
    rarity: ShopItemRarity.RARE,
    status: ShopItemStatus.EQUIPPED,
    pricePoints: 700,
    priceMoney: 1.99,
    imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=400&fit=crop'
    ],
    tags: ['princesas', 'cabeçalho', 'elegante'],
    purchasedAt: new Date('2024-01-12'),
    equippedAt: new Date('2024-01-12'),
    purchasedBy: 'user-123',
    availableForChildren: true,
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-12')
  },
  // Temas de Animais
  {
    id: 'forest-animals-theme',
    name: 'Tema Animais da Floresta',
    description: 'Aventura na floresta com animais fofos!',
    category: ShopItemCategory.ANIMAL,
    type: ShopItemType.THEME,
    rarity: ShopItemRarity.COMMON,
    status: ShopItemStatus.AVAILABLE,
    pricePoints: 900,
    priceMoney: 2.49,
    imageUrl: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop'
    ],
    tags: ['floresta', 'animais', 'natureza'],
    isNew: true,
    purchasedBy: undefined,
    availableForChildren: false,
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20')
  },
  // Temas Espaciais
  {
    id: 'space-adventure-theme',
    name: 'Tema Aventura Espacial',
    description: 'Explora o universo com este tema espacial incrível!',
    category: ShopItemCategory.SPACE,
    type: ShopItemType.THEME,
    rarity: ShopItemRarity.EPIC,
    status: ShopItemStatus.AVAILABLE,
    pricePoints: 1600,
    priceMoney: 3.99,
    imageUrl: 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=800&h=600&fit=crop'
    ],
    tags: ['espaço', 'planetas', 'estrelas', 'nave'],
    isPopular: true,
    purchasedBy: undefined,
    availableForChildren: false,
    createdAt: new Date('2024-01-14'),
    updatedAt: new Date('2024-01-14')
  },
  // Temas específicos para crianças
  {
    id: 'unicorn-theme',
    name: 'Tema Unicórnio Mágico',
    description: 'Um mundo mágico cheio de unicórnios e arco-íris! Perfeito para sonhar!',
    category: ShopItemCategory.FANTASY,
    type: ShopItemType.THEME,
    rarity: ShopItemRarity.EPIC,
    status: ShopItemStatus.AVAILABLE,
    pricePoints: 800,
    priceMoney: 1.99,
    imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop'
    ],
    tags: ['unicórnio', 'magia', 'arco-íris', 'fantasia'],
    isNew: true,
    purchasedBy: undefined,
    availableForChildren: true,
    createdAt: new Date('2024-01-21'),
    updatedAt: new Date('2024-01-21')
  },
  {
    id: 'dinosaur-theme',
    name: 'Tema Dinossauros',
    description: 'Viaja no tempo e conhece os incríveis dinossauros! Rugido garantido!',
    category: ShopItemCategory.ANIMAL,
    type: ShopItemType.THEME,
    rarity: ShopItemRarity.RARE,
    status: ShopItemStatus.PURCHASED,
    pricePoints: 600,
    priceMoney: 1.49,
    imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop'
    ],
    tags: ['dinossauros', 'pré-história', 'aventura'],
    isPopular: true,
    purchasedBy: 'child-123',
    availableForChildren: true,
    createdAt: new Date('2024-01-18'),
    updatedAt: new Date('2024-01-18')
  },
  {
    id: 'ocean-theme',
    name: 'Tema Fundo do Mar',
    description: 'Mergulha no oceano e descobre criaturas marinhas fantásticas!',
    category: ShopItemCategory.ANIMAL,
    type: ShopItemType.THEME,
    rarity: ShopItemRarity.COMMON,
    status: ShopItemStatus.AVAILABLE,
    pricePoints: 400,
    priceMoney: 0.99,
    imageUrl: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop',
    previewImages: [
      'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&h=600&fit=crop'
    ],
    tags: ['oceano', 'peixes', 'mar', 'azul'],
    isNew: false,
    purchasedBy: undefined,
    availableForChildren: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  }
];

// Mock Shop Stats
export const mockShopStats: ShopStats = {
  totalItems: 42,
  availableItems: 32,
  purchasedItems: 8,
  equippedItems: 2,
  totalSpent: 8200,
  pointsSpent: 7700,
  moneySpent: 4.99
};

// Mock User Balance
export const mockUserBalance: UserBalance = {
  points: 3500,
  money: 15.50,
  lastUpdated: new Date('2024-01-21')
};

// Mock Shop Purchases
export const mockShopPurchases: ShopPurchase[] = [
  {
    id: 'purchase-1',
    itemId: 'wonder-woman-theme',
    itemName: 'Tema Wonder Woman',
    price: 1800,
    paymentMethod: PaymentMethod.POINTS,
    purchasedAt: new Date('2024-01-20'),
    userId: 'user-123',
    userName: 'João Silva'
  },
  {
    id: 'purchase-2',
    itemId: 'ariel-theme',
    itemName: 'Tema Pequena Sereia',
    price: 1400,
    paymentMethod: PaymentMethod.POINTS,
    purchasedAt: new Date('2024-01-05'),
    userId: 'user-123',
    userName: 'João Silva'
  },
  {
    id: 'purchase-3',
    itemId: 'princess-castle-bg',
    itemName: 'Fundo Castelo Princesa',
    price: 400,
    paymentMethod: PaymentMethod.POINTS,
    purchasedAt: new Date('2024-01-15'),
    userId: 'user-123',
    userName: 'João Silva'
  },
  {
    id: 'purchase-4',
    itemId: 'princess-header',
    itemName: 'Header Princesas',
    price: 700,
    paymentMethod: PaymentMethod.POINTS,
    purchasedAt: new Date('2024-01-12'),
    userId: 'user-123',
    userName: 'João Silva'
  }
];
