{"indexes": [{"collectionGroup": "chatRooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "arrayConfig": "CONTAINS"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "chatId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "typing", "queryScope": "COLLECTION", "fields": [{"fieldPath": "chatId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}], "fieldOverrides": []}